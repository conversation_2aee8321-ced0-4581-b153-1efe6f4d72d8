name: Publish Develop Image
on:
  push:
    branches: [develop]
    paths:
      - "app/**"
      - "migrations/**"
      - "Dockerfile"
      - "entrypoint.sh"
  workflow_dispatch:
    inputs:
      tag:
        description: The build tag to push.
        required: true
        default: "develop"
jobs:
  push_develop_to_registry:
    if: ${{ github.event_name == 'push' && github.ref_name == 'develop' }}
    name: Build & Push [develop]
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v1.6.0

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Push to Docker Hub
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: Dockerfile
          tags: shaneisrael/fireshare:develop

  manual_push_to_registry:
    if: ${{ github.event_name == 'workflow_dispatch' }}
    name: Build & Push [${{ github.event.inputs.tag }}]
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - uses: docker/setup-buildx-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Push to Docker Hub
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: Dockerfile
          tags: shaneisrael/fireshare:${{ github.event.inputs.tag }}
