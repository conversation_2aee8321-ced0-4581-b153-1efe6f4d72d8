user  nginx;
worker_processes  2;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

thread_pool default threads=32 max_queue=65536;

events {
    worker_connections  1024;
    accept_mutex off;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$http_x_forwarded_for - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$remote_addr"';

    server_tokens off;

    access_log  /var/log/nginx/access.log  main;

    sendfile   off;
    sendfile_max_chunk 512k;
    tcp_nopush on;
    
    keepalive_timeout  65;
    include /etc/nginx/conf.d/*.conf;

    proxy_cache_path /var/cache/nginx keys_zone=PROXYCACHE:100m inactive=60m max_size=500m;
    proxy_cache_key  "$scheme$request_method$host$request_uri";
    add_header X-Cache-Status $upstream_cache_status;
    proxy_cache_min_uses 0;
    proxy_cache_revalidate on;
    proxy_cache_use_stale error timeout updating http_500;
    proxy_cache_background_update on;
    proxy_cache_lock on;

    server {
        listen 80 default;
        server_name default;
        

        gzip off;
        gzip_min_length     256;
        gzip_proxied        any;

        gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/manifest+json
        application/x-font-ttf
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

        root /processed/;
        aio threads=default;

        location /_content/ {
            rewrite ^/_content/(.*)$ /$1 break;
            proxy_cache        PROXYCACHE;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404      1m;
            root /processed/;
        }
        
        location /_content/video/ {
            mp4;
            mp4_buffer_size 1m;
            mp4_max_buffer_size 20m;
            directio 2048m;
            directio_alignment 4k;
            rewrite ^/_content/video/(.*)$ /$1 break;
            root /processed/video_links/;
        }

        location / {
            proxy_cache        PROXYCACHE;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404      1m;
            root /app/build;
            index  index.html;
        }

        location ~ /api/.*$ {
            proxy_pass              http://localhost:5000;
            proxy_http_version      1.1;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        Host $http_host;
            proxy_connect_timeout   60s;
            proxy_read_timeout      999999s;
            client_max_body_size 0;
        }

        location ~ /w/.*$ {
            proxy_pass              http://localhost:5000;
            proxy_http_version      1.1;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        Host $http_host;
            proxy_connect_timeout   60s;
            proxy_read_timeout      60s;
        }
    }

}
