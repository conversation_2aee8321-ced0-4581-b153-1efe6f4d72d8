# Fireshare Vulnerability Testing Suite

## ⚠️ **LEGAL DISCLAIMER**
This testing suite is designed for **authorized security testing only**. Only use this on systems you own or have explicit written permission to test. Unauthorized testing may violate laws and regulations.

## 🎯 **Overview**
This script tests all 19 vulnerabilities identified in the Fireshare application, including:
- **Critical**: SQL Injection, LDAP Injection, Command Injection, Path Traversal
- **High**: Missing Authorization, Input Validation Bypass
- **Medium**: File Type Validation, JSON Validation Errors
- **Low**: Security Configuration Issues

## 📋 **Prerequisites**
```bash
pip install requests
```

## 🚀 **Usage**

### Basic Usage (All Tests)
```bash
python fireshare_vulnerability_tester.py http://localhost:8080
```

### With Authentication
```bash
python fireshare_vulnerability_tester.py http://localhost:8080 -u admin -p admin
```

### Run Specific Test
```bash
# Test SQL injection only
python fireshare_vulnerability_tester.py http://localhost:8080 --test sql-public

# Test command injection
python fireshare_vulnerability_tester.py http://localhost:8080 --test command -u admin -p admin
```

## 🔍 **Available Tests**

| Test ID | Vulnerability | Authentication Required | CVSS Score |
|---------|---------------|------------------------|------------|
| `sql-public` | SQL Injection (Public) | No | 9.8 |
| `sql-auth` | SQL Injection (Authenticated) | Yes | 9.1 |
| `ldap` | LDAP Injection | No | 9.1 |
| `command` | Command Injection | No/Yes | 9.8 |
| `path` | Path Traversal (Video) | No | 8.6 |
| `directory` | Directory Traversal | No | 7.5 |
| `admin` | Missing Admin Authorization | Yes | 8.1 |
| `input` | Input Validation Bypass | Yes | 7.1 |
| `json` | JSON Validation Errors | No | 5.3 |
| `filetype` | File Type Validation | No/Yes | 6.1 |

## 📊 **Expected Output**

### Successful Vulnerability Detection
```
[14:30:15] [INFO] Starting Fireshare Vulnerability Assessment
[14:30:15] [INFO] Target: http://localhost:8080
[14:30:15] [SUCCESS] Authentication successful
==================================================
[14:30:16] [TEST] Testing SQL Injection in Public Video Sorting (Unauthenticated)
[14:30:16] [CRITICAL] 🚨 SQL INJECTION CONFIRMED - Payload: 1' OR '1'='1
[14:30:16] [DETAIL] Response: {"error": "syntax error at or near..."}
==================================================
[14:30:17] [TEST] Testing Command Injection in File Upload
[14:30:17] [CRITICAL] 🚨 COMMAND INJECTION POSSIBLE - File uploaded with malicious name
==================================================
[14:30:18] [INFO] VULNERABILITY ASSESSMENT COMPLETE
[14:30:18] [SUMMARY] Vulnerabilities confirmed: 8
[14:30:18] [CRITICAL] 🚨 CONFIRMED VULNERABILITIES:
[14:30:18] [CRITICAL]   - SQL Injection (Public)
[14:30:18] [CRITICAL]   - Command Injection (Upload)
[14:30:18] [CRITICAL]   - Directory Traversal (Folder Size)
```

## 🛡️ **Testing Strategy**

### Phase 1: Unauthenticated Tests
1. **SQL Injection (Public)** - Most critical, no auth required
2. **Directory Traversal** - Information disclosure
3. **LDAP Injection** - Authentication bypass
4. **Command Injection** - Via public upload
5. **JSON Validation** - Application stability

### Phase 2: Authenticated Tests (if credentials available)
1. **SQL Injection (Authenticated)** - Database access
2. **Missing Admin Authorization** - Privilege escalation
3. **Input Validation Bypass** - Data integrity
4. **Command Injection** - Via admin upload
5. **File Type Validation** - Upload security

## 🔧 **Customization**

### Adding Custom Payloads
Edit the payload lists in each test method:

```python
# SQL Injection payloads
payloads = [
    "1' OR '1'='1",
    "1; SELECT 1; --",
    "YOUR_CUSTOM_PAYLOAD"
]

# LDAP Injection payloads
ldap_payloads = [
    "*)(uid=*))(|(uid=*",
    "YOUR_LDAP_PAYLOAD"
]
```

### Testing Different Endpoints
Modify the endpoint lists:

```python
# Command injection endpoints
endpoints = ["/api/upload/public", "/api/custom/upload"]
```

## 🚨 **Critical Vulnerabilities Explained**

### 1. SQL Injection (Public) - CVSS 9.8
- **Location**: `/api/videos/public?sort=PAYLOAD`
- **Impact**: Complete database compromise
- **Test**: Injects SQL into sort parameter
- **Detection**: HTTP 500 errors or SQL error messages

### 2. Command Injection - CVSS 9.8
- **Location**: File upload endpoints
- **Impact**: Remote code execution
- **Test**: Uploads files with shell metacharacters in filename
- **Detection**: Successful upload with malicious filename

### 3. LDAP Injection - CVSS 9.1
- **Location**: `/api/login` authentication
- **Impact**: Authentication bypass
- **Test**: Injects LDAP filter metacharacters
- **Detection**: HTTP 200 response with invalid credentials

### 4. Directory Traversal - CVSS 7.5
- **Location**: `/api/folder-size?path=PAYLOAD`
- **Impact**: Filesystem reconnaissance
- **Test**: Accesses system directories
- **Detection**: Size information returned for system paths

## 🔍 **Troubleshooting**

### Connection Issues
```bash
# Test basic connectivity
curl -I http://localhost:8080

# Check if Fireshare is running
docker ps | grep fireshare
```

### Authentication Problems
```bash
# Test manual login
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'
```

### False Negatives
- Some vulnerabilities may not trigger in all environments
- Check application logs for evidence of exploitation
- Verify file system changes for command injection tests

## 📝 **Reporting**

### Generate Test Report
```bash
python fireshare_vulnerability_tester.py http://localhost:8080 -u admin -p admin > vulnerability_report.txt 2>&1
```

### Evidence Collection
For confirmed vulnerabilities, collect:
1. **Request/Response pairs** showing exploitation
2. **Application logs** showing errors or execution
3. **File system evidence** for command injection
4. **Database queries** for SQL injection

## 🛠️ **Integration with CI/CD**

### Jenkins Pipeline
```groovy
stage('Security Testing') {
    steps {
        sh 'python fireshare_vulnerability_tester.py ${TEST_URL} -u ${TEST_USER} -p ${TEST_PASS}'
    }
}
```

### GitHub Actions
```yaml
- name: Run Vulnerability Tests
  run: |
    python fireshare_vulnerability_tester.py ${{ secrets.TEST_URL }} \
      -u ${{ secrets.TEST_USER }} -p ${{ secrets.TEST_PASS }}
```

## 🔒 **Responsible Disclosure**

If testing a third-party Fireshare instance:
1. **Get written permission** before testing
2. **Document findings** thoroughly
3. **Report to maintainers** via security contact
4. **Allow time for fixes** before public disclosure
5. **Follow coordinated disclosure** practices

---

**Remember**: This tool is for authorized testing only. Always follow responsible disclosure practices and respect system owners' policies.
